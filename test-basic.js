// Basic test để kiểm tra server
const axios = require('axios');

async function testBasic() {
    try {
        console.log('Testing server connection...');
        
        // Test health endpoint
        const healthResponse = await axios.get('http://localhost:3000/health');
        console.log('✅ Health check:', healthResponse.data.message);
        
        // Test duty log endpoint
        console.log('\nTesting duty log...');
        const dutyData = {
            identifier: 'steam:test123',
            name: 'Test Player',
            job: 'police',
            grade: 1,
            status: 'on_duty'
        };
        
        const dutyResponse = await axios.post('http://localhost:3000/api/fivem/duty-log', dutyData);
        console.log('✅ Duty log:', dutyResponse.data.message);
        
        // Test active sessions
        console.log('\nTesting active sessions...');
        const activeResponse = await axios.get('http://localhost:3000/api/fivem/active-sessions');
        console.log('✅ Active sessions:', activeResponse.data.total);
        
        // Test disconnect
        console.log('\nTesting disconnect...');
        const disconnectData = {
            identifier: 'steam:test123',
            name: 'Test Player',
            reason: 'test'
        };
        
        const disconnectResponse = await axios.post('http://localhost:3000/api/fivem/player-disconnect', disconnectData);
        console.log('✅ Disconnect:', disconnectResponse.data.message);
        
        // Check active sessions again
        console.log('\nChecking active sessions after disconnect...');
        const activeResponse2 = await axios.get('http://localhost:3000/api/fivem/active-sessions');
        console.log('✅ Active sessions after disconnect:', activeResponse2.data.total);
        
        console.log('\n🎉 All tests passed!');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        }
    }
}

testBasic();
