// Test script để kiểm tra tính năng tính thời gian duty
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testDutyTimeCalculation() {
    console.log('🚀 Testing Duty Time Calculation...\n');

    try {
        // Test case: Tạo một session duty hoàn chỉnh
        console.log('1. Creating a complete duty session...');
        
        const player = {
            identifier: 'steam:test123456789',
            name: 'Test Player - Cảnh Sát',
            job: 'police',
            grade: 2
        };

        // Bước 1: On duty lúc 11:00
        const onDutyTime = new Date();
        onDutyTime.setHours(11, 0, 0, 0);
        
        const onDutyLog = {
            ...player,
            status: 'on_duty',
            timestamp: onDutyTime.toISOString()
        };

        const onDutyResponse = await axios.post(`${BASE_URL}/fivem/duty-log`, onDutyLog);
        console.log('✅ On duty at 11:00:', onDutyResponse.data.message);
        
        // Delay nhỏ để đảm bảo thời gian khác nhau
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Bước 2: Off duty lúc 12:00 (1 giờ sau)
        const offDutyTime = new Date();
        offDutyTime.setHours(12, 0, 0, 0);
        
        const offDutyLog = {
            ...player,
            status: 'off_duty',
            timestamp: offDutyTime.toISOString()
        };

        const offDutyResponse = await axios.post(`${BASE_URL}/fivem/duty-log`, offDutyLog);
        console.log('✅ Off duty at 12:00:', offDutyResponse.data.message);
        console.log('');

        // Test case 2: Session dài hơn
        console.log('2. Creating a longer duty session...');
        
        const player2 = {
            identifier: 'steam:test987654321',
            name: 'Test Player 2 - Bác Sĩ',
            job: 'ambulance',
            grade: 1
        };

        // On duty lúc 8:00
        const onDutyTime2 = new Date();
        onDutyTime2.setHours(8, 0, 0, 0);
        
        await axios.post(`${BASE_URL}/fivem/duty-log`, {
            ...player2,
            status: 'on_duty',
            timestamp: onDutyTime2.toISOString()
        });
        console.log('✅ Player 2 on duty at 8:00');

        await new Promise(resolve => setTimeout(resolve, 1000));

        // Off duty lúc 17:30 (9.5 giờ sau)
        const offDutyTime2 = new Date();
        offDutyTime2.setHours(17, 30, 0, 0);
        
        await axios.post(`${BASE_URL}/fivem/duty-log`, {
            ...player2,
            status: 'off_duty',
            timestamp: offDutyTime2.toISOString()
        });
        console.log('✅ Player 2 off duty at 17:30');
        console.log('');

        // Test case 3: Multiple sessions trong ngày
        console.log('3. Creating multiple sessions for one player...');
        
        const player3 = {
            identifier: 'steam:test555666777',
            name: 'Test Player 3 - Thợ Máy',
            job: 'mechanic',
            grade: 0
        };

        // Session 1: 9:00 - 12:00 (3 giờ)
        await axios.post(`${BASE_URL}/fivem/duty-log`, {
            ...player3,
            status: 'on_duty',
            timestamp: new Date().setHours(9, 0, 0, 0)
        });
        
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await axios.post(`${BASE_URL}/fivem/duty-log`, {
            ...player3,
            status: 'off_duty',
            timestamp: new Date().setHours(12, 0, 0, 0)
        });

        // Session 2: 13:00 - 18:00 (5 giờ)
        await axios.post(`${BASE_URL}/fivem/duty-log`, {
            ...player3,
            status: 'on_duty',
            timestamp: new Date().setHours(13, 0, 0, 0)
        });
        
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await axios.post(`${BASE_URL}/fivem/duty-log`, {
            ...player3,
            status: 'off_duty',
            timestamp: new Date().setHours(18, 0, 0, 0)
        });

        console.log('✅ Created multiple sessions for Player 3');
        console.log('');

        // Kiểm tra kết quả
        console.log('4. Checking calculated results...');
        
        // Lấy player stats
        const playerStatsResponse = await axios.get(`${BASE_URL}/fivem/player-stats`);
        console.log('✅ Player Statistics:');
        
        playerStatsResponse.data.data.forEach((stat, index) => {
            const hours = parseFloat(stat.total_hours || 0);
            const sessions = stat.total_sessions || 0;
            const formattedTime = stat.formatted_time || '0h 0m';
            
            console.log(`   ${index + 1}. ${stat.name}`);
            console.log(`      - Job: ${stat.job}`);
            console.log(`      - Total sessions: ${sessions}`);
            console.log(`      - Total time: ${hours.toFixed(1)} hours (${stat.total_minutes} minutes)`);
            console.log(`      - Formatted: ${formattedTime}`);
            console.log(`      - Average per session: ${stat.avg_session_minutes} minutes`);
            console.log('');
        });

        // Lấy duty sessions
        console.log('5. Checking duty sessions...');
        const sessionsResponse = await axios.get(`${BASE_URL}/fivem/duty-sessions?limit=10`);
        console.log('✅ Duty Sessions:');
        
        sessionsResponse.data.data.forEach((session, index) => {
            const startTime = new Date(session.start_time).toLocaleTimeString();
            const endTime = session.end_time ? 
                new Date(session.end_time).toLocaleTimeString() : 'Still active';
            const duration = session.formatted_duration || 'Active';
            
            console.log(`   ${index + 1}. ${session.name} (${session.job})`);
            console.log(`      - Start: ${startTime}`);
            console.log(`      - End: ${endTime}`);
            console.log(`      - Duration: ${duration}`);
            console.log(`      - Status: ${session.status}`);
            console.log('');
        });

        console.log('🎉 Duty time calculation test completed!');
        console.log('');
        console.log('📊 Expected results:');
        console.log('   - Player 1 (Police): 1 hour total');
        console.log('   - Player 2 (Ambulance): 9.5 hours total');
        console.log('   - Player 3 (Mechanic): 8 hours total (3h + 5h)');
        console.log('');
        console.log('🌐 Check the web dashboard at http://localhost:3000');
        console.log('   - Go to "Phiên Duty" tab to see detailed sessions');
        console.log('   - Check "Thống kê Players" tab for total times');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy test
testDutyTimeCalculation();
