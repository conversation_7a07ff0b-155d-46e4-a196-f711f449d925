const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static('public'));

// Lưu trữ dữ liệu tạm thời (trong thực tế nên dùng database)
let attendanceRecords = [];
let dutyLogs = []; // Lưu trữ duty logs từ FiveM

// Endpoint để nhận thông tin onduty
app.post('/api/onduty', (req, res) => {
    try {
        const { employeeId, employeeName, timestamp, location, note } = req.body;
        
        // Validate dữ liệu đầu vào
        if (!employeeId || !employeeName) {
            return res.status(400).json({
                success: false,
                message: 'Employee ID và Employee Name là bắt buộc'
            });
        }

        // Tạo record onduty
        const ondutyRecord = {
            id: Date.now().toString(),
            employeeId,
            employeeName,
            type: 'onduty',
            timestamp: timestamp || new Date().toISOString(),
            location: location || null,
            note: note || null,
            createdAt: new Date().toISOString()
        };

        // Lưu vào mảng tạm thời
        attendanceRecords.push(ondutyRecord);

        console.log('Onduty record created:', ondutyRecord);

        res.status(201).json({
            success: true,
            message: 'Đã ghi nhận thông tin onduty thành công',
            data: ondutyRecord
        });

    } catch (error) {
        console.error('Error in onduty endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi xử lý onduty',
            error: error.message
        });
    }
});

// Endpoint để nhận thông tin offduty
app.post('/api/offduty', (req, res) => {
    try {
        const { employeeId, employeeName, timestamp, location, note } = req.body;
        
        // Validate dữ liệu đầu vào
        if (!employeeId || !employeeName) {
            return res.status(400).json({
                success: false,
                message: 'Employee ID và Employee Name là bắt buộc'
            });
        }

        // Tạo record offduty
        const offdutyRecord = {
            id: Date.now().toString(),
            employeeId,
            employeeName,
            type: 'offduty',
            timestamp: timestamp || new Date().toISOString(),
            location: location || null,
            note: note || null,
            createdAt: new Date().toISOString()
        };

        // Lưu vào mảng tạm thời
        attendanceRecords.push(offdutyRecord);

        console.log('Offduty record created:', offdutyRecord);

        res.status(201).json({
            success: true,
            message: 'Đã ghi nhận thông tin offduty thành công',
            data: offdutyRecord
        });

    } catch (error) {
        console.error('Error in offduty endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi xử lý offduty',
            error: error.message
        });
    }
});

// Endpoint để lấy danh sách tất cả records
app.get('/api/attendance', (req, res) => {
    try {
        const { employeeId, type, date } = req.query;
        
        let filteredRecords = attendanceRecords;

        // Filter theo employeeId nếu có
        if (employeeId) {
            filteredRecords = filteredRecords.filter(record => 
                record.employeeId === employeeId
            );
        }

        // Filter theo type (onduty/offduty) nếu có
        if (type) {
            filteredRecords = filteredRecords.filter(record => 
                record.type === type
            );
        }

        // Filter theo ngày nếu có
        if (date) {
            filteredRecords = filteredRecords.filter(record => 
                record.timestamp.startsWith(date)
            );
        }

        res.json({
            success: true,
            message: 'Lấy danh sách attendance thành công',
            data: filteredRecords,
            total: filteredRecords.length
        });

    } catch (error) {
        console.error('Error in get attendance endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy danh sách attendance',
            error: error.message
        });
    }
});

// Endpoint để lấy thông tin một record cụ thể
app.get('/api/attendance/:id', (req, res) => {
    try {
        const { id } = req.params;
        const record = attendanceRecords.find(r => r.id === id);

        if (!record) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy record với ID này'
            });
        }

        res.json({
            success: true,
            message: 'Lấy thông tin record thành công',
            data: record
        });

    } catch (error) {
        console.error('Error in get single attendance endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy thông tin record',
            error: error.message
        });
    }
});

// ===== FIVEM DUTY SYSTEM ENDPOINTS =====

// Endpoint để nhận duty logs từ FiveM server
app.post('/api/fivem/duty-log', (req, res) => {
    try {
        const { identifier, name, job, grade, status, timestamp } = req.body;

        // Validate dữ liệu đầu vào
        if (!identifier || !name || !job || !status) {
            return res.status(400).json({
                success: false,
                message: 'identifier, name, job và status là bắt buộc'
            });
        }

        // Validate status
        if (!['on_duty', 'off_duty'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'status phải là on_duty hoặc off_duty'
            });
        }

        // Tạo duty log record
        const dutyLogRecord = {
            id: Date.now().toString(),
            identifier,
            name,
            job,
            grade: grade || 0,
            status,
            timestamp: timestamp || new Date().toISOString(),
            createdAt: new Date().toISOString()
        };

        // Lưu vào mảng duty logs
        dutyLogs.push(dutyLogRecord);

        console.log('Duty log received from FiveM:', dutyLogRecord);

        res.status(201).json({
            success: true,
            message: 'Duty log đã được ghi nhận thành công',
            data: dutyLogRecord
        });

    } catch (error) {
        console.error('Error in FiveM duty log endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi xử lý duty log',
            error: error.message
        });
    }
});

// Endpoint để lấy danh sách duty logs
app.get('/api/fivem/duty-logs', (req, res) => {
    try {
        const { identifier, job, status, date, limit } = req.query;

        let filteredLogs = dutyLogs;

        // Filter theo identifier nếu có
        if (identifier) {
            filteredLogs = filteredLogs.filter(log =>
                log.identifier === identifier
            );
        }

        // Filter theo job nếu có
        if (job) {
            filteredLogs = filteredLogs.filter(log =>
                log.job === job
            );
        }

        // Filter theo status nếu có
        if (status) {
            filteredLogs = filteredLogs.filter(log =>
                log.status === status
            );
        }

        // Filter theo ngày nếu có
        if (date) {
            filteredLogs = filteredLogs.filter(log =>
                log.timestamp.startsWith(date)
            );
        }

        // Sắp xếp theo thời gian mới nhất
        filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        // Giới hạn số lượng kết quả
        if (limit) {
            const limitNum = parseInt(limit);
            if (!isNaN(limitNum) && limitNum > 0) {
                filteredLogs = filteredLogs.slice(0, limitNum);
            }
        }

        res.json({
            success: true,
            message: 'Lấy danh sách duty logs thành công',
            data: filteredLogs,
            total: filteredLogs.length
        });

    } catch (error) {
        console.error('Error in get duty logs endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy duty logs',
            error: error.message
        });
    }
});

// Endpoint để lấy thống kê duty
app.get('/api/fivem/duty-stats', (req, res) => {
    try {
        const { job, date } = req.query;

        let filteredLogs = dutyLogs;

        // Filter theo job nếu có
        if (job) {
            filteredLogs = filteredLogs.filter(log => log.job === job);
        }

        // Filter theo ngày nếu có
        if (date) {
            filteredLogs = filteredLogs.filter(log =>
                log.timestamp.startsWith(date)
            );
        }

        // Tính toán thống kê
        const stats = {
            total: filteredLogs.length,
            onDuty: filteredLogs.filter(log => log.status === 'on_duty').length,
            offDuty: filteredLogs.filter(log => log.status === 'off_duty').length,
            byJob: {},
            byPlayer: {},
            recentActivity: filteredLogs
                .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                .slice(0, 10)
        };

        // Thống kê theo job
        filteredLogs.forEach(log => {
            if (!stats.byJob[log.job]) {
                stats.byJob[log.job] = { onDuty: 0, offDuty: 0, total: 0 };
            }
            stats.byJob[log.job][log.status === 'on_duty' ? 'onDuty' : 'offDuty']++;
            stats.byJob[log.job].total++;
        });

        // Thống kê theo player
        filteredLogs.forEach(log => {
            if (!stats.byPlayer[log.identifier]) {
                stats.byPlayer[log.identifier] = {
                    name: log.name,
                    onDuty: 0,
                    offDuty: 0,
                    total: 0
                };
            }
            stats.byPlayer[log.identifier][log.status === 'on_duty' ? 'onDuty' : 'offDuty']++;
            stats.byPlayer[log.identifier].total++;
        });

        res.json({
            success: true,
            message: 'Lấy thống kê duty thành công',
            data: stats
        });

    } catch (error) {
        console.error('Error in duty stats endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy thống kê duty',
            error: error.message
        });
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Server đang hoạt động bình thường',
        timestamp: new Date().toISOString()
    });
});

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Web Điểm Danh & FiveM Duty API Server',
        version: '1.0.0',
        endpoints: {
            // Basic attendance endpoints
            'POST /api/onduty': 'Ghi nhận thông tin onduty',
            'POST /api/offduty': 'Ghi nhận thông tin offduty',
            'GET /api/attendance': 'Lấy danh sách tất cả records',
            'GET /api/attendance/:id': 'Lấy thông tin một record cụ thể',

            // FiveM duty system endpoints
            'POST /api/fivem/duty-log': 'Nhận duty logs từ FiveM server',
            'GET /api/fivem/duty-logs': 'Lấy danh sách duty logs',
            'GET /api/fivem/duty-stats': 'Lấy thống kê duty',

            // System endpoints
            'GET /health': 'Kiểm tra trạng thái server',
            'GET /': 'API documentation'
        }
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`Server đang chạy tại http://localhost:${PORT}`);
    console.log('Endpoints có sẵn:');
    console.log('- POST /api/onduty - Ghi nhận onduty');
    console.log('- POST /api/offduty - Ghi nhận offduty');
    console.log('- GET /api/attendance - Lấy danh sách records');
    console.log('- GET /health - Health check');
});

module.exports = app;
