const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const db = require('./database');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static('public'));

// Lưu trữ dữ liệu tạm thời (trong thực tế nên dùng database)
let attendanceRecords = [];
let dutyLogs = []; // Lưu trữ duty logs từ FiveM

// Endpoint để nhận thông tin onduty
app.post('/api/onduty', (req, res) => {
    try {
        const { employeeId, employeeName, timestamp, location, note } = req.body;
        
        // Validate dữ liệu đầu vào
        if (!employeeId || !employeeName) {
            return res.status(400).json({
                success: false,
                message: 'Employee ID và Employee Name là bắt buộc'
            });
        }

        // Tạo record onduty
        const ondutyRecord = {
            id: Date.now().toString(),
            employeeId,
            employeeName,
            type: 'onduty',
            timestamp: timestamp || new Date().toISOString(),
            location: location || null,
            note: note || null,
            createdAt: new Date().toISOString()
        };

        // Lưu vào mảng tạm thời
        attendanceRecords.push(ondutyRecord);

        console.log('Onduty record created:', ondutyRecord);

        res.status(201).json({
            success: true,
            message: 'Đã ghi nhận thông tin onduty thành công',
            data: ondutyRecord
        });

    } catch (error) {
        console.error('Error in onduty endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi xử lý onduty',
            error: error.message
        });
    }
});

// Endpoint để nhận thông tin offduty
app.post('/api/offduty', (req, res) => {
    try {
        const { employeeId, employeeName, timestamp, location, note } = req.body;
        
        // Validate dữ liệu đầu vào
        if (!employeeId || !employeeName) {
            return res.status(400).json({
                success: false,
                message: 'Employee ID và Employee Name là bắt buộc'
            });
        }

        // Tạo record offduty
        const offdutyRecord = {
            id: Date.now().toString(),
            employeeId,
            employeeName,
            type: 'offduty',
            timestamp: timestamp || new Date().toISOString(),
            location: location || null,
            note: note || null,
            createdAt: new Date().toISOString()
        };

        // Lưu vào mảng tạm thời
        attendanceRecords.push(offdutyRecord);

        console.log('Offduty record created:', offdutyRecord);

        res.status(201).json({
            success: true,
            message: 'Đã ghi nhận thông tin offduty thành công',
            data: offdutyRecord
        });

    } catch (error) {
        console.error('Error in offduty endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi xử lý offduty',
            error: error.message
        });
    }
});

// Endpoint để lấy danh sách tất cả records
app.get('/api/attendance', (req, res) => {
    try {
        const { employeeId, type, date } = req.query;
        
        let filteredRecords = attendanceRecords;

        // Filter theo employeeId nếu có
        if (employeeId) {
            filteredRecords = filteredRecords.filter(record => 
                record.employeeId === employeeId
            );
        }

        // Filter theo type (onduty/offduty) nếu có
        if (type) {
            filteredRecords = filteredRecords.filter(record => 
                record.type === type
            );
        }

        // Filter theo ngày nếu có
        if (date) {
            filteredRecords = filteredRecords.filter(record => 
                record.timestamp.startsWith(date)
            );
        }

        res.json({
            success: true,
            message: 'Lấy danh sách attendance thành công',
            data: filteredRecords,
            total: filteredRecords.length
        });

    } catch (error) {
        console.error('Error in get attendance endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy danh sách attendance',
            error: error.message
        });
    }
});

// Endpoint để lấy thông tin một record cụ thể
app.get('/api/attendance/:id', (req, res) => {
    try {
        const { id } = req.params;
        const record = attendanceRecords.find(r => r.id === id);

        if (!record) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy record với ID này'
            });
        }

        res.json({
            success: true,
            message: 'Lấy thông tin record thành công',
            data: record
        });

    } catch (error) {
        console.error('Error in get single attendance endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy thông tin record',
            error: error.message
        });
    }
});

// ===== FIVEM DUTY SYSTEM ENDPOINTS =====

// Endpoint để nhận duty logs từ FiveM server
app.post('/api/fivem/duty-log', async (req, res) => {
    try {
        const { identifier, name, job, grade, status, timestamp } = req.body;

        // Validate dữ liệu đầu vào
        if (!identifier || !name || !job || !status) {
            return res.status(400).json({
                success: false,
                message: 'identifier, name, job và status là bắt buộc'
            });
        }

        // Validate status
        if (!['on_duty', 'off_duty'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'status phải là on_duty hoặc off_duty'
            });
        }

        const finalTimestamp = timestamp || new Date().toISOString();
        const finalGrade = grade || 0;

        // Lưu vào database
        const logId = await db.insertDutyLog(identifier, name, job, finalGrade, status, finalTimestamp);

        // Xử lý duty session (tính thời gian online)
        await db.processDutySession(identifier, name, job, finalGrade, status, finalTimestamp);

        // Cập nhật thống kê
        const stats = await db.updateStatistics(identifier, name, job);

        const dutyLogRecord = {
            id: logId,
            identifier,
            name,
            job,
            grade: finalGrade,
            status,
            timestamp: finalTimestamp,
            stats: stats
        };

        console.log('Duty log saved to database:', dutyLogRecord);

        res.status(201).json({
            success: true,
            message: 'Duty log đã được ghi nhận thành công',
            data: dutyLogRecord
        });

    } catch (error) {
        console.error('Error in FiveM duty log endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi xử lý duty log',
            error: error.message
        });
    }
});

// Endpoint để lấy danh sách duty logs
app.get('/api/fivem/duty-logs', async (req, res) => {
    try {
        const { identifier, job, status, date, date_from, date_to, player, limit } = req.query;

        const filters = {};
        if (identifier) filters.identifier = identifier;
        if (job) filters.job = job;
        if (status) filters.status = status;
        if (date) filters.date = date;
        if (date_from) filters.date_from = date_from;
        if (date_to) filters.date_to = date_to;
        if (player) filters.player = player;
        if (limit) filters.limit = limit;

        const logs = await db.getDutyLogs(filters);

        res.json({
            success: true,
            message: 'Lấy danh sách duty logs thành công',
            data: logs,
            total: logs.length,
            filters: filters // Trả về filters để debug
        });

    } catch (error) {
        console.error('Error in get duty logs endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy duty logs',
            error: error.message
        });
    }
});

// Endpoint để lấy thống kê duty
app.get('/api/fivem/duty-stats', async (req, res) => {
    try {
        const { job, date } = req.query;

        const filters = {};
        if (job) filters.job = job;
        if (date) filters.date = date;

        const overallStats = await db.getOverallStats(filters);

        res.json({
            success: true,
            message: 'Lấy thống kê duty thành công',
            data: overallStats
        });

    } catch (error) {
        console.error('Error in duty stats endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy thống kê duty',
            error: error.message
        });
    }
});

// Endpoint để lấy thống kê thời gian online của players
app.get('/api/fivem/player-stats', async (req, res) => {
    try {
        const { identifier, job } = req.query;

        const filters = {};
        if (identifier) filters.identifier = identifier;
        if (job) filters.job = job;

        const playerStats = await db.getDutyStats(filters);

        res.json({
            success: true,
            message: 'Lấy thống kê player thành công',
            data: playerStats
        });

    } catch (error) {
        console.error('Error in player stats endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy thống kê player',
            error: error.message
        });
    }
});

// Endpoint để lấy duty sessions chi tiết
app.get('/api/fivem/duty-sessions', async (req, res) => {
    try {
        const { identifier, job, date, status, limit } = req.query;

        const filters = {};
        if (identifier) filters.identifier = identifier;
        if (job) filters.job = job;
        if (date) filters.date = date;
        if (status) filters.status = status;
        if (limit) filters.limit = limit;

        const sessions = await db.getDutySessions(filters);

        res.json({
            success: true,
            message: 'Lấy duty sessions thành công',
            data: sessions,
            total: sessions.length
        });

    } catch (error) {
        console.error('Error in duty sessions endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy duty sessions',
            error: error.message
        });
    }
});

// Endpoint để lấy thống kê theo ngày
app.get('/api/fivem/daily-stats', async (req, res) => {
    try {
        const { identifier, job, date } = req.query;

        const filters = {};
        if (identifier) filters.identifier = identifier;
        if (job) filters.job = job;
        if (date) filters.date = date;

        const dailyStats = await db.getDailyDutyStats(filters);

        res.json({
            success: true,
            message: 'Lấy thống kê theo ngày thành công',
            data: dailyStats
        });

    } catch (error) {
        console.error('Error in daily stats endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy thống kê theo ngày',
            error: error.message
        });
    }
});

// Endpoint để xử lý player disconnect (auto off-duty)
app.post('/api/fivem/player-disconnect', async (req, res) => {
    try {
        const { identifier, name, reason } = req.body;

        if (!identifier) {
            return res.status(400).json({
                success: false,
                message: 'identifier là bắt buộc'
            });
        }

        // Tự động kết thúc tất cả sessions đang active của player này
        const result = await db.handlePlayerDisconnect(identifier, name, reason);

        res.json({
            success: true,
            message: 'Xử lý disconnect thành công',
            data: result
        });

    } catch (error) {
        console.error('Error in player disconnect endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi xử lý disconnect',
            error: error.message
        });
    }
});

// Endpoint để tự động kết thúc các sessions cũ (cleanup)
app.post('/api/fivem/cleanup-sessions', async (req, res) => {
    try {
        const { maxHours = 24 } = req.body;

        const result = await db.cleanupOldSessions(maxHours);

        res.json({
            success: true,
            message: 'Cleanup sessions thành công',
            data: result
        });

    } catch (error) {
        console.error('Error in cleanup sessions endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi cleanup sessions',
            error: error.message
        });
    }
});

// Endpoint để lấy danh sách sessions đang active
app.get('/api/fivem/active-sessions', async (req, res) => {
    try {
        const activeSessions = await db.getActiveSessions();

        res.json({
            success: true,
            message: 'Lấy active sessions thành công',
            data: activeSessions,
            total: activeSessions.length
        });

    } catch (error) {
        console.error('Error in active sessions endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy active sessions',
            error: error.message
        });
    }
});

// Endpoint để kiểm tra trạng thái duty của player khi join server
app.post('/api/fivem/check-duty-status', async (req, res) => {
    try {
        const { identifier, name } = req.body;

        if (!identifier) {
            return res.status(400).json({
                success: false,
                message: 'identifier là bắt buộc'
            });
        }

        const dutyStatus = await db.checkPlayerDutyStatus(identifier, name);

        res.json({
            success: true,
            message: 'Kiểm tra duty status thành công',
            data: dutyStatus
        });

    } catch (error) {
        console.error('Error in check duty status endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi kiểm tra duty status',
            error: error.message
        });
    }
});

// Endpoint để xử lý player reconnect (tiếp tục session cũ)
app.post('/api/fivem/player-reconnect', async (req, res) => {
    try {
        const { identifier, name, action } = req.body;

        if (!identifier || !action) {
            return res.status(400).json({
                success: false,
                message: 'identifier và action là bắt buộc'
            });
        }

        const result = await db.handlePlayerReconnect(identifier, name, action);

        res.json({
            success: true,
            message: 'Xử lý reconnect thành công',
            data: result
        });

    } catch (error) {
        console.error('Error in player reconnect endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi xử lý reconnect',
            error: error.message
        });
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Server đang hoạt động bình thường',
        timestamp: new Date().toISOString()
    });
});

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Web Điểm Danh & FiveM Duty API Server',
        version: '1.0.0',
        endpoints: {
            // Basic attendance endpoints
            'POST /api/onduty': 'Ghi nhận thông tin onduty',
            'POST /api/offduty': 'Ghi nhận thông tin offduty',
            'GET /api/attendance': 'Lấy danh sách tất cả records',
            'GET /api/attendance/:id': 'Lấy thông tin một record cụ thể',

            // FiveM duty system endpoints
            'POST /api/fivem/duty-log': 'Nhận duty logs từ FiveM server',
            'GET /api/fivem/duty-logs': 'Lấy danh sách duty logs',
            'GET /api/fivem/duty-stats': 'Lấy thống kê duty tổng quan',
            'GET /api/fivem/player-stats': 'Lấy thống kê thời gian online của players',
            'GET /api/fivem/duty-sessions': 'Lấy chi tiết các phiên duty (thời gian cụ thể)',
            'GET /api/fivem/daily-stats': 'Lấy thống kê theo ngày',
            'GET /api/fivem/active-sessions': 'Lấy danh sách sessions đang active',
            'POST /api/fivem/player-disconnect': 'Xử lý player disconnect (auto off-duty)',
            'POST /api/fivem/cleanup-sessions': 'Cleanup sessions cũ (timeout)',
            'POST /api/fivem/check-duty-status': 'Kiểm tra trạng thái duty khi player join',
            'POST /api/fivem/player-reconnect': 'Xử lý player reconnect (tiếp tục/kết thúc session)',

            // System endpoints
            'GET /health': 'Kiểm tra trạng thái server',
            'GET /': 'API documentation'
        }
    });
});

// Khởi tạo database và start server
async function startServer() {
    try {
        // Test kết nối database
        const dbConnected = await db.testConnection();
        if (!dbConnected) {
            console.error('❌ Không thể kết nối database. Server sẽ không khởi động.');
            process.exit(1);
        }

        // Khởi tạo bảng database
        const tablesInitialized = await db.initializeTables();
        if (!tablesInitialized) {
            console.error('❌ Không thể khởi tạo bảng database. Server sẽ không khởi động.');
            process.exit(1);
        }

        // Start server
        app.listen(PORT, () => {
            console.log(`🚀 Server đang chạy tại http://localhost:${PORT}`);
            console.log('📊 Web Dashboard: http://localhost:' + PORT);
            console.log('');
            console.log('📋 Endpoints có sẵn:');
            console.log('  Basic Attendance:');
            console.log('  - POST /api/onduty - Ghi nhận onduty');
            console.log('  - POST /api/offduty - Ghi nhận offduty');
            console.log('  - GET /api/attendance - Lấy danh sách records');
            console.log('');
            console.log('  FiveM Integration:');
            console.log('  - POST /api/fivem/duty-log - Nhận duty logs từ FiveM');
            console.log('  - GET /api/fivem/duty-logs - Lấy danh sách duty logs');
            console.log('  - GET /api/fivem/duty-stats - Thống kê duty tổng quan');
            console.log('  - GET /api/fivem/player-stats - Thống kê thời gian online');
            console.log('');
            console.log('  System:');
            console.log('  - GET /health - Health check');
            console.log('  - GET / - API documentation');
        });

    } catch (error) {
        console.error('❌ Lỗi khởi tạo server:', error);
        process.exit(1);
    }
}

// Khởi động server
startServer();

module.exports = app;
