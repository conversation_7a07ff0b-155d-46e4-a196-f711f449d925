// Test tính năng menu on duty khi join game
const axios = require('axios');

const BASE_URL = 'https://med.kaitomc.site/api';

async function testOnDutyMenu() {
    console.log('🎮 Testing On Duty Menu on Game Join...\n');

    try {
        // 1. Tạo test scenario - player on duty rồi disconnect
        console.log('1. Setting up test scenario...');
        const testPlayer = {
            identifier: 'steam:on_duty_menu_test_789',
            name: 'On Duty Menu Test Player',
            job: 'ambulance',
            grade: 2,
            status: 'on_duty'
        };

        // Player on duty
        const onDutyResponse = await axios.post(`${BASE_URL}/fivem/duty-log`, testPlayer);
        console.log('✅ Test player on duty:', onDutyResponse.data.message);
        
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 2. Kiểm tra active session
        console.log('\n2. Checking active session...');
        const activeResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        
        let foundTestPlayer = false;
        if (activeResponse.data.data) {
            activeResponse.data.data.forEach(session => {
                if (session.identifier === testPlayer.identifier) {
                    foundTestPlayer = true;
                    console.log(`✅ Found test player: ${session.name} (${session.job}) - ${session.time_active}`);
                }
            });
        }

        if (!foundTestPlayer) {
            console.log('⚠️  Test player not found in active sessions');
        }

        // 3. Simulate disconnect (auto off-duty)
        console.log('\n3. Simulating player disconnect (auto off-duty)...');
        const disconnectData = {
            identifier: testPlayer.identifier,
            name: testPlayer.name,
            reason: 'Lost connection - menu test'
        };

        const disconnectResponse = await axios.post(`${BASE_URL}/fivem/player-disconnect`, disconnectData);
        console.log('✅ Disconnect processed:', disconnectResponse.data.message);
        
        if (disconnectResponse.data.data) {
            console.log('📊 Sessions ended:', disconnectResponse.data.data.sessionsEnded);
        }

        // 4. Verify player is off duty on web
        console.log('\n4. Verifying player is off duty on web...');
        const activeResponse2 = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        
        let stillActive = false;
        if (activeResponse2.data.data) {
            activeResponse2.data.data.forEach(session => {
                if (session.identifier === testPlayer.identifier) {
                    stillActive = true;
                }
            });
        }

        console.log(`✅ Player removed from active sessions: ${!stillActive ? 'YES' : 'NO'}`);

        // 5. Check duty logs for auto off-duty
        console.log('\n5. Checking duty logs for auto off-duty...');
        const logsResponse = await axios.get(`${BASE_URL}/fivem/duty-logs?limit=10`);
        
        let foundAutoOffDuty = false;
        let foundManualOnDuty = false;
        
        logsResponse.data.data.slice(0, 10).forEach((log, index) => {
            const isTestPlayer = log.identifier === testPlayer.identifier;
            
            if (isTestPlayer) {
                const time = new Date(log.timestamp).toLocaleString();
                const statusEmoji = log.status === 'on_duty' ? '🟢' : '🔴';
                const actionType = log.status === 'off_duty' ? '🤖 AUTO OFF' : '👤 MANUAL ON';
                
                console.log(`   ${actionType} ${statusEmoji} ${log.name} (${log.job}) - ${time}`);
                
                if (log.status === 'off_duty') foundAutoOffDuty = true;
                if (log.status === 'on_duty') foundManualOnDuty = true;
            }
        });

        console.log(`   Found manual on-duty: ${foundManualOnDuty ? '✅' : '❌'}`);
        console.log(`   Found auto off-duty: ${foundAutoOffDuty ? '✅' : '❌'}`);

        console.log('\n🎉 On Duty Menu Test Setup Completed!');
        console.log('\n📋 Test Scenario Summary:');
        console.log(`✅ Player on duty created: ${onDutyResponse.status === 200 ? 'SUCCESS' : 'FAILED'}`);
        console.log(`✅ Found in active sessions: ${foundTestPlayer ? 'SUCCESS' : 'FAILED'}`);
        console.log(`✅ Disconnect processed: ${disconnectResponse.status === 200 ? 'SUCCESS' : 'FAILED'}`);
        console.log(`✅ Removed from active: ${!stillActive ? 'SUCCESS' : 'FAILED'}`);
        console.log(`✅ Auto off-duty logged: ${foundAutoOffDuty ? 'SUCCESS' : 'FAILED'}`);

        console.log('\n🎮 What happens in FiveM now:');
        console.log('');
        console.log('📱 When player disconnects:');
        console.log('   1. 🎮 Game: Job switches (ambulance → offambulance)');
        console.log('   2. 🌐 Web: Session ends automatically');
        console.log('   3. 📝 Logs: Auto off-duty entry created');
        console.log('');
        console.log('🔄 When player reconnects (joins game):');
        console.log('   1. 🎮 Game: Player spawns with off-duty job (offambulance)');
        console.log('   2. 📱 Menu: On Duty Menu appears automatically');
        console.log('   3. 🟢 Option 1: "BẬT DUTY" - Go on duty immediately');
        console.log('   4. ❌ Option 2: "ĐỂ SAU" - Skip, go to marker later');
        console.log('');
        console.log('🎯 Menu Features:');
        console.log('   - 🎨 Beautiful native UI with colors');
        console.log('   - 🎮 Controller/keyboard navigation');
        console.log('   - 🟢 Green highlight for "BẬT DUTY"');
        console.log('   - 🔵 Blue highlight for "ĐỂ SAU"');
        console.log('   - 🔊 Sound effects for navigation');
        console.log('   - 📱 Responsive to player job type');
        console.log('');
        console.log('🔧 Implementation Details:');
        console.log('   - Menu only shows for off-duty jobs (offpolice, offambulance, etc.)');
        console.log('   - Automatic detection when player joins');
        console.log('   - Instant duty activation without going to marker');
        console.log('   - Proper job switching (offambulance → ambulance)');
        console.log('   - Integration with existing duty system');
        console.log('');
        console.log('🧪 Testing Commands (Admin only):');
        console.log('   - /testonduty - Test the on duty menu');
        console.log('   - /testdutymenu - Test the reconnect menu');
        console.log('');
        console.log('🚀 To apply in FiveM:');
        console.log('1. Copy updated client.lua to your resource');
        console.log('2. Copy updated server.lua to your resource');
        console.log('3. Restart resource: restart esx_duty_advanced');
        console.log('4. Test: go on duty → disconnect → reconnect');
        console.log('5. Verify: on duty menu appears when joining');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy test
testOnDutyMenu();
