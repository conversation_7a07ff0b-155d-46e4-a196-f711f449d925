// Test để kiểm tra disconnect handling sau khi sửa lỗi
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testDisconnectFix() {
    console.log('🔧 Testing Disconnect Fix...\n');

    try {
        // 1. Tạo player on duty (giống như từ FiveM)
        console.log('1. Player goes on duty...');
        const player = {
            identifier: 'steam:1100001417d37d0', // Giống như "Chị Phiến"
            name: '<PERSON><PERSON>ến',
            job: 'ambulance', // Y Tế
            grade: 4,
            status: 'on_duty'
        };

        const onDutyResponse = await axios.post(`${BASE_URL}/fivem/duty-log`, player);
        console.log('✅ Player on duty:', onDutyResponse.data.message);
        
        // 2. Kiểm tra active sessions
        console.log('\n2. Checking active sessions...');
        const activeResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse.data.total}`);
        
        if (activeResponse.data.total > 0) {
            console.log('   Current active sessions:');
            activeResponse.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
            });
        }

        // 3. Simulate disconnect (như khi player out game)
        console.log('\n3. Player disconnects (out game)...');
        const disconnectData = {
            identifier: player.identifier,
            name: player.name,
            reason: 'Lost connection'
        };

        const disconnectResponse = await axios.post(`${BASE_URL}/fivem/player-disconnect`, disconnectData);
        console.log('✅ Disconnect processed:', disconnectResponse.data.message);
        
        if (disconnectResponse.data.data) {
            console.log('📊 Sessions ended:', disconnectResponse.data.data.sessionsEnded);
            console.log('⏰ Disconnect time:', new Date(disconnectResponse.data.data.disconnectTime).toLocaleString());
        }

        // 4. Kiểm tra active sessions sau disconnect
        console.log('\n4. Checking active sessions after disconnect...');
        const activeResponse2 = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse2.data.total}`);
        
        if (activeResponse2.data.total === 0) {
            console.log('🎉 PERFECT! No active sessions - disconnect handled correctly!');
            console.log('✅ Player "Chị Phiến" is no longer showing as online');
        } else {
            console.log('⚠️  Still have active sessions:');
            activeResponse2.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
            });
        }

        // 5. Kiểm tra duty logs để xem auto off-duty
        console.log('\n5. Checking recent duty logs...');
        const logsResponse = await axios.get(`${BASE_URL}/fivem/duty-logs?limit=5`);
        console.log('✅ Recent logs:');
        
        logsResponse.data.data.slice(0, 5).forEach((log, index) => {
            const time = new Date(log.timestamp).toLocaleString();
            const isAutoOffDuty = log.status === 'off_duty' && log.name === player.name;
            const marker = isAutoOffDuty ? '🤖 AUTO' : '👤 MANUAL';
            
            console.log(`   ${index + 1}. ${marker} ${log.name} (${log.job}) - ${log.status} - ${time}`);
        });

        // 6. Kiểm tra duty sessions (completed sessions)
        console.log('\n6. Checking completed duty sessions...');
        const sessionsResponse = await axios.get(`${BASE_URL}/fivem/duty-sessions?limit=3`);
        console.log('✅ Recent completed sessions:');
        
        sessionsResponse.data.data.slice(0, 3).forEach((session, index) => {
            const startTime = new Date(session.start_time).toLocaleString();
            const endTime = session.end_time ? new Date(session.end_time).toLocaleString() : 'Still active';
            const duration = session.duration_minutes ? `${Math.floor(session.duration_minutes / 60)}h ${session.duration_minutes % 60}m` : 'N/A';
            
            console.log(`   ${index + 1}. ${session.name} (${session.job})`);
            console.log(`      Start: ${startTime}`);
            console.log(`      End: ${endTime}`);
            console.log(`      Duration: ${duration}`);
            console.log(`      Status: ${session.status}`);
        });

        console.log('\n🎉 Disconnect fix test completed!');
        console.log('\n📋 What should happen now:');
        console.log('✅ 1. Player "Chị Phiến" should NOT appear in "Đang Online" tab');
        console.log('✅ 2. A completed session should appear in "Phiên Duty" tab');
        console.log('✅ 3. An auto off-duty log should appear in "Duty Logs" tab');
        console.log('✅ 4. Statistics should be updated with the session time');
        
        console.log('\n🌐 Check the web dashboard now:');
        console.log('   - Refresh the page');
        console.log('   - Go to "Đang Online" tab - should be empty or not show "Chị Phiến"');
        console.log('   - Go to "Phiên Duty" tab - should show completed session');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy test
testDisconnectFix();
