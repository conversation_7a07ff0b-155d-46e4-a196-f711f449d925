// Test tính năng auto off-duty khi player disconnect
const axios = require('axios');

const BASE_URL = 'https://med.kaitomc.site/api';

async function testAutoOffDuty() {
    console.log('🔄 Testing Auto Off-Duty System...\n');

    try {
        // 1. Tạo test player on duty
        console.log('1. Creating test player on duty...');
        const testPlayer = {
            identifier: 'steam:auto_off_test_123',
            name: 'Test Auto Off Player',
            job: 'police',
            grade: 2,
            status: 'on_duty'
        };

        const onDutyResponse = await axios.post(`${BASE_URL}/fivem/duty-log`, testPlayer);
        console.log('✅ Test player on duty:', onDutyResponse.data.message);
        
        // Wait 2 seconds để session được tạo
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 2. Kiểm tra active sessions
        console.log('\n2. Checking active sessions...');
        const activeResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse.data.total}`);
        
        let foundTestPlayer = false;
        if (activeResponse.data.data && activeResponse.data.data.length > 0) {
            console.log('   Current active sessions:');
            activeResponse.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
                if (session.identifier === testPlayer.identifier) {
                    foundTestPlayer = true;
                }
            });
        }

        if (foundTestPlayer) {
            console.log('✅ Test player found in active sessions');
        } else {
            console.log('⚠️  Test player not found in active sessions');
        }

        // 3. Simulate player disconnect (auto off-duty)
        console.log('\n3. Simulating player disconnect (auto off-duty)...');
        const disconnectData = {
            identifier: testPlayer.identifier,
            name: testPlayer.name,
            reason: 'Lost connection - auto test'
        };

        const disconnectResponse = await axios.post(`${BASE_URL}/fivem/player-disconnect`, disconnectData);
        console.log('✅ Disconnect processed:', disconnectResponse.data.message);
        
        if (disconnectResponse.data.data) {
            console.log('📊 Sessions ended:', disconnectResponse.data.data.sessionsEnded);
            console.log('⏰ Disconnect time:', new Date(disconnectResponse.data.data.disconnectTime).toLocaleString());
        }

        // 4. Kiểm tra active sessions sau disconnect
        console.log('\n4. Checking active sessions after disconnect...');
        const activeResponse2 = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse2.data.total}`);
        
        let stillFoundTestPlayer = false;
        if (activeResponse2.data.data && activeResponse2.data.data.length > 0) {
            console.log('   Remaining active sessions:');
            activeResponse2.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
                if (session.identifier === testPlayer.identifier) {
                    stillFoundTestPlayer = true;
                }
            });
        }

        if (!stillFoundTestPlayer) {
            console.log('🎉 PERFECT! Test player automatically removed from active sessions');
        } else {
            console.log('❌ ISSUE: Test player still in active sessions');
        }

        // 5. Kiểm tra duty logs để xem auto off-duty entry
        console.log('\n5. Checking duty logs for auto off-duty entry...');
        const logsResponse = await axios.get(`${BASE_URL}/fivem/duty-logs?limit=10`);
        console.log('✅ Recent duty logs:');
        
        let foundAutoOffDuty = false;
        logsResponse.data.data.slice(0, 10).forEach((log, index) => {
            const time = new Date(log.timestamp).toLocaleString();
            const isTestPlayer = log.identifier === testPlayer.identifier;
            const isAutoOffDuty = isTestPlayer && log.status === 'off_duty';
            
            if (isAutoOffDuty) {
                foundAutoOffDuty = true;
            }
            
            const marker = isTestPlayer ? (log.status === 'off_duty' ? '🤖 AUTO OFF' : '👤 MANUAL ON') : '   ';
            console.log(`   ${index + 1}. ${marker} ${log.name} (${log.job}) - ${log.status} - ${time}`);
        });

        if (foundAutoOffDuty) {
            console.log('✅ Auto off-duty log entry found');
        } else {
            console.log('⚠️  Auto off-duty log entry not found');
        }

        // 6. Kiểm tra completed sessions
        console.log('\n6. Checking completed duty sessions...');
        const sessionsResponse = await axios.get(`${BASE_URL}/fivem/duty-sessions?limit=5`);
        console.log('✅ Recent completed sessions:');
        
        let foundCompletedSession = false;
        sessionsResponse.data.data.slice(0, 5).forEach((session, index) => {
            const isTestPlayer = session.identifier === testPlayer.identifier;
            const startTime = new Date(session.start_time).toLocaleString();
            const endTime = session.end_time ? new Date(session.end_time).toLocaleString() : 'Still active';
            const duration = session.duration_minutes ? `${Math.floor(session.duration_minutes / 60)}h ${session.duration_minutes % 60}m` : 'N/A';
            
            if (isTestPlayer && session.end_time) {
                foundCompletedSession = true;
            }
            
            const marker = isTestPlayer ? '🎯 TEST' : '   ';
            console.log(`   ${index + 1}. ${marker} ${session.name} (${session.job})`);
            console.log(`      Start: ${startTime}`);
            console.log(`      End: ${endTime}`);
            console.log(`      Duration: ${duration}`);
            console.log(`      Status: ${session.status}`);
        });

        if (foundCompletedSession) {
            console.log('✅ Completed session found for test player');
        } else {
            console.log('⚠️  Completed session not found for test player');
        }

        console.log('\n🎉 Auto Off-Duty Test Completed!');
        console.log('\n📋 Summary:');
        console.log(`✅ Player on duty: ${onDutyResponse.status === 200 ? 'SUCCESS' : 'FAILED'}`);
        console.log(`✅ Found in active sessions: ${foundTestPlayer ? 'SUCCESS' : 'FAILED'}`);
        console.log(`✅ Disconnect processed: ${disconnectResponse.status === 200 ? 'SUCCESS' : 'FAILED'}`);
        console.log(`✅ Removed from active: ${!stillFoundTestPlayer ? 'SUCCESS' : 'FAILED'}`);
        console.log(`✅ Auto off-duty logged: ${foundAutoOffDuty ? 'SUCCESS' : 'FAILED'}`);
        console.log(`✅ Session completed: ${foundCompletedSession ? 'SUCCESS' : 'FAILED'}`);
        
        const allSuccess = onDutyResponse.status === 200 && 
                          foundTestPlayer && 
                          disconnectResponse.status === 200 && 
                          !stillFoundTestPlayer && 
                          foundAutoOffDuty && 
                          foundCompletedSession;
        
        console.log(`\n🎯 Overall Result: ${allSuccess ? '🎉 ALL TESTS PASSED!' : '⚠️  SOME TESTS FAILED'}`);
        
        console.log('\n🔧 For FiveM Integration:');
        console.log('1. Copy updated server.lua to your FiveM resource');
        console.log('2. Restart the resource: restart esx_duty_advanced');
        console.log('3. Test in-game: go on duty, then disconnect');
        console.log('4. Check web dashboard - player should auto off-duty');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy test
testAutoOffDuty();
