// Test script để kiểm tra xử lý disconnect và auto off-duty
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testDisconnectHandling() {
    console.log('🚀 Testing Disconnect Handling & Auto Off-Duty...\n');

    try {
        // Test case 1: Tạo player on duty
        console.log('1. Creating players on duty...');
        
        const players = [
            {
                identifier: 'steam:disconnect_test_1',
                name: 'Player 1 - Sẽ Disconnect',
                job: 'police',
                grade: 2
            },
            {
                identifier: 'steam:disconnect_test_2',
                name: 'Player 2 - Sẽ Disconnect',
                job: 'ambulance',
                grade: 1
            },
            {
                identifier: 'steam:disconnect_test_3',
                name: 'Player 3 - Normal Off',
                job: 'mechanic',
                grade: 0
            }
        ];

        // Tất cả players on duty
        for (const player of players) {
            await axios.post(`${BASE_URL}/fivem/duty-log`, {
                ...player,
                status: 'on_duty'
            });
            console.log(`✅ ${player.name} is now on duty`);
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        console.log('');

        // Kiểm tra active sessions
        console.log('2. Checking active sessions...');
        const activeResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse.data.total}`);
        
        activeResponse.data.data.forEach((session, index) => {
            console.log(`   ${index + 1}. ${session.name} (${session.job}) - Active for ${session.time_active}`);
        });
        console.log('');

        // Test case 2: Simulate disconnect cho player 1
        console.log('3. Simulating disconnect for Player 1...');
        const disconnectResponse1 = await axios.post(`${BASE_URL}/fivem/player-disconnect`, {
            identifier: 'steam:disconnect_test_1',
            name: 'Player 1 - Sẽ Disconnect',
            reason: 'Lost connection'
        });
        
        console.log('✅ Disconnect response:', disconnectResponse1.data.message);
        console.log('📊 Sessions ended:', disconnectResponse1.data.data.sessionsEnded);
        console.log('');

        // Test case 3: Simulate disconnect cho player 2
        console.log('4. Simulating disconnect for Player 2...');
        const disconnectResponse2 = await axios.post(`${BASE_URL}/fivem/player-disconnect`, {
            identifier: 'steam:disconnect_test_2',
            name: 'Player 2 - Sẽ Disconnect',
            reason: 'Game crash'
        });
        
        console.log('✅ Disconnect response:', disconnectResponse2.data.message);
        console.log('📊 Sessions ended:', disconnectResponse2.data.data.sessionsEnded);
        console.log('');

        // Test case 4: Player 3 off duty bình thường
        console.log('5. Player 3 going off duty normally...');
        await axios.post(`${BASE_URL}/fivem/duty-log`, {
            identifier: 'steam:disconnect_test_3',
            name: 'Player 3 - Normal Off',
            job: 'mechanic',
            grade: 0,
            status: 'off_duty'
        });
        console.log('✅ Player 3 went off duty normally');
        console.log('');

        // Kiểm tra active sessions sau disconnect
        console.log('6. Checking active sessions after disconnects...');
        const activeResponse2 = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse2.data.total}`);
        
        if (activeResponse2.data.total === 0) {
            console.log('🎉 Perfect! No active sessions remaining - all handled correctly');
        } else {
            activeResponse2.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - Still active`);
            });
        }
        console.log('');

        // Test case 5: Tạo session cũ để test cleanup
        console.log('7. Creating old session for cleanup test...');
        
        // Tạo session với thời gian cũ (25 giờ trước)
        const oldTime = new Date();
        oldTime.setHours(oldTime.getHours() - 25);
        
        await axios.post(`${BASE_URL}/fivem/duty-log`, {
            identifier: 'steam:old_session_test',
            name: 'Old Session Player',
            job: 'police',
            grade: 1,
            status: 'on_duty',
            timestamp: oldTime.toISOString()
        });
        console.log('✅ Created old session (25 hours ago)');
        console.log('');

        // Test cleanup
        console.log('8. Testing cleanup old sessions...');
        const cleanupResponse = await axios.post(`${BASE_URL}/fivem/cleanup-sessions`, {
            maxHours: 24
        });
        
        console.log('✅ Cleanup response:', cleanupResponse.data.message);
        console.log('📊 Sessions cleaned:', cleanupResponse.data.data.sessionsEnded);
        console.log('');

        // Kiểm tra duty logs để xác nhận auto off-duty
        console.log('9. Checking duty logs for auto off-duty entries...');
        const logsResponse = await axios.get(`${BASE_URL}/fivem/duty-logs?limit=10`);
        console.log('✅ Recent duty logs:');
        
        logsResponse.data.data.slice(0, 8).forEach((log, index) => {
            const time = new Date(log.timestamp).toLocaleTimeString();
            const autoOffDuty = log.timestamp === disconnectResponse1.data.data.disconnectTime || 
                               log.timestamp === disconnectResponse2.data.data.disconnectTime;
            const marker = autoOffDuty ? '🤖 AUTO' : '👤 MANUAL';
            
            console.log(`   ${index + 1}. ${marker} ${log.name} (${log.job}) - ${log.status} - ${time}`);
        });
        console.log('');

        // Kiểm tra player stats
        console.log('10. Checking updated player stats...');
        const statsResponse = await axios.get(`${BASE_URL}/fivem/player-stats`);
        console.log('✅ Player statistics updated:');
        
        statsResponse.data.data.slice(0, 5).forEach((stat, index) => {
            const hours = parseFloat(stat.total_hours || 0);
            const sessions = stat.total_sessions || 0;
            const formattedTime = stat.formatted_time || '0h 0m';
            
            console.log(`   ${index + 1}. ${stat.name} (${stat.job})`);
            console.log(`      - Sessions: ${sessions}, Time: ${formattedTime}`);
        });
        console.log('');

        console.log('🎉 Disconnect handling test completed successfully!');
        console.log('');
        console.log('✅ Features tested:');
        console.log('   - Auto off-duty when player disconnects');
        console.log('   - Proper time calculation for disconnected sessions');
        console.log('   - Cleanup of old sessions (timeout)');
        console.log('   - Statistics update after auto off-duty');
        console.log('   - Duty logs creation for auto off-duty');
        console.log('');
        console.log('🌐 Check the web dashboard:');
        console.log('   - "Đang Online" tab should show no active sessions');
        console.log('   - "Phiên Duty" tab shows completed sessions');
        console.log('   - "Thống kê Players" shows updated times');
        console.log('   - Look for 🤖 AUTO markers in recent logs');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy test
testDisconnectHandling();
