const mysql = require('mysql2/promise');
require('dotenv').config();

// Tạo connection pool
const pool = mysql.createPool({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: process.env.DB_PORT || 3306,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    // Removed invalid options: acquireTimeout, timeout, reconnect
    idleTimeout: 600000,
    enableKeepAlive: true,
    keepAliveInitialDelay: 0
});

// Kiểm tra kết nối
async function testConnection() {
    try {
        const connection = await pool.getConnection();
        console.log('✅ Kết nối database thành công!');
        connection.release();
        return true;
    } catch (error) {
        console.error('❌ Lỗi kết nối database:', error.message);
        return false;
    }
}

// Tạo bảng nếu chưa tồn tại
async function initializeTables() {
    try {
        // Bảng duty_logs - lư<PERSON> tất cả logs on/off duty
        await pool.execute(`
            CREATE TABLE IF NOT EXISTS duty_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                identifier VARCHAR(100) NOT NULL,
                name VARCHAR(100) NOT NULL,
                job VARCHAR(50) NOT NULL,
                grade INT DEFAULT 0,
                status ENUM('on_duty', 'off_duty') NOT NULL,
                timestamp DATETIME NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_identifier (identifier),
                INDEX idx_job (job),
                INDEX idx_status (status),
                INDEX idx_timestamp (timestamp)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // Bảng duty_sessions - tính toán thời gian online
        await pool.execute(`
            CREATE TABLE IF NOT EXISTS duty_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                identifier VARCHAR(100) NOT NULL,
                name VARCHAR(100) NOT NULL,
                job VARCHAR(50) NOT NULL,
                grade INT DEFAULT 0,
                start_time DATETIME NOT NULL,
                end_time DATETIME NULL,
                duration_minutes INT DEFAULT 0,
                status ENUM('active', 'completed') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_identifier (identifier),
                INDEX idx_job (job),
                INDEX idx_status (status),
                INDEX idx_start_time (start_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // Bảng duty_statistics - thống kê tổng hợp
        await pool.execute(`
            CREATE TABLE IF NOT EXISTS duty_statistics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                identifier VARCHAR(100) NOT NULL,
                name VARCHAR(100) NOT NULL,
                job VARCHAR(50) NOT NULL,
                total_sessions INT DEFAULT 0,
                total_minutes INT DEFAULT 0,
                total_hours DECIMAL(10,2) DEFAULT 0,
                last_on_duty DATETIME NULL,
                last_off_duty DATETIME NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_player_job (identifier, job),
                INDEX idx_identifier (identifier),
                INDEX idx_job (job)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        console.log('✅ Khởi tạo bảng database thành công!');
        return true;
    } catch (error) {
        console.error('❌ Lỗi khởi tạo bảng:', error.message);
        return false;
    }
}

// Ghi duty log
async function insertDutyLog(identifier, name, job, grade, status, timestamp) {
    try {
        const [result] = await pool.execute(
            'INSERT INTO duty_logs (identifier, name, job, grade, status, timestamp) VALUES (?, ?, ?, ?, ?, ?)',
            [identifier, name, job, grade, status, timestamp]
        );
        return result.insertId;
    } catch (error) {
        console.error('Lỗi insert duty log:', error);
        throw error;
    }
}

// Xử lý duty session
async function processDutySession(identifier, name, job, grade, status, timestamp) {
    try {
        if (status === 'on_duty') {
            // Kết thúc session cũ nếu có (trường hợp không off_duty đúng cách)
            await pool.execute(
                `UPDATE duty_sessions 
                 SET end_time = ?, duration_minutes = TIMESTAMPDIFF(MINUTE, start_time, ?), status = 'completed'
                 WHERE identifier = ? AND job = ? AND status = 'active'`,
                [timestamp, timestamp, identifier, job]
            );

            // Tạo session mới
            const [result] = await pool.execute(
                'INSERT INTO duty_sessions (identifier, name, job, grade, start_time) VALUES (?, ?, ?, ?, ?)',
                [identifier, name, job, grade, timestamp]
            );
            return result.insertId;
        } else if (status === 'off_duty') {
            // Kết thúc session hiện tại
            const [result] = await pool.execute(
                `UPDATE duty_sessions 
                 SET end_time = ?, duration_minutes = TIMESTAMPDIFF(MINUTE, start_time, ?), status = 'completed'
                 WHERE identifier = ? AND job = ? AND status = 'active'`,
                [timestamp, timestamp, identifier, job]
            );
            return result.affectedRows;
        }
    } catch (error) {
        console.error('Lỗi process duty session:', error);
        throw error;
    }
}

// Cập nhật thống kê
async function updateStatistics(identifier, name, job) {
    try {
        // Tính tổng thời gian và sessions
        const [stats] = await pool.execute(
            `SELECT 
                COUNT(*) as total_sessions,
                COALESCE(SUM(duration_minutes), 0) as total_minutes,
                COALESCE(SUM(duration_minutes) / 60, 0) as total_hours
             FROM duty_sessions 
             WHERE identifier = ? AND job = ? AND status = 'completed'`,
            [identifier, job]
        );

        // Lấy thời gian on/off duty gần nhất
        const [lastTimes] = await pool.execute(
            `SELECT 
                MAX(CASE WHEN status = 'on_duty' THEN timestamp END) as last_on_duty,
                MAX(CASE WHEN status = 'off_duty' THEN timestamp END) as last_off_duty
             FROM duty_logs 
             WHERE identifier = ? AND job = ?`,
            [identifier, job]
        );

        const statData = stats[0];
        const timeData = lastTimes[0];

        // Insert hoặc update statistics
        await pool.execute(
            `INSERT INTO duty_statistics 
             (identifier, name, job, total_sessions, total_minutes, total_hours, last_on_duty, last_off_duty)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)
             ON DUPLICATE KEY UPDATE
             name = VALUES(name),
             total_sessions = VALUES(total_sessions),
             total_minutes = VALUES(total_minutes),
             total_hours = VALUES(total_hours),
             last_on_duty = VALUES(last_on_duty),
             last_off_duty = VALUES(last_off_duty),
             updated_at = CURRENT_TIMESTAMP`,
            [
                identifier, name, job,
                statData.total_sessions,
                statData.total_minutes,
                statData.total_hours,
                timeData.last_on_duty,
                timeData.last_off_duty
            ]
        );

        return statData;
    } catch (error) {
        console.error('Lỗi update statistics:', error);
        throw error;
    }
}

// Lấy duty logs với phân trang và filter
async function getDutyLogs(filters = {}) {
    try {
        let query = 'SELECT * FROM duty_logs WHERE 1=1';
        let params = [];

        if (filters.identifier) {
            query += ' AND identifier = ?';
            params.push(filters.identifier);
        }

        if (filters.job) {
            query += ' AND job = ?';
            params.push(filters.job);
        }

        if (filters.status) {
            query += ' AND status = ?';
            params.push(filters.status);
        }

        // Date filtering - specific date takes priority
        if (filters.date) {
            query += ' AND DATE(timestamp) = ?';
            params.push(filters.date);
        } else {
            // Date range filtering
            if (filters.date_from) {
                query += ' AND DATE(timestamp) >= ?';
                params.push(filters.date_from);
            }
            if (filters.date_to) {
                query += ' AND DATE(timestamp) <= ?';
                params.push(filters.date_to);
            }
        }

        // Player search (name or identifier)
        if (filters.player) {
            query += ' AND (name LIKE ? OR identifier LIKE ?)';
            const searchTerm = `%${filters.player}%`;
            params.push(searchTerm, searchTerm);
        }

        query += ' ORDER BY timestamp DESC';

        if (filters.limit) {
            query += ' LIMIT ?';
            params.push(parseInt(filters.limit));
        }

        const [rows] = await pool.execute(query, params);
        return rows;
    } catch (error) {
        console.error('Lỗi get duty logs:', error);
        throw error;
    }
}

// Lấy thống kê duty với thông tin chi tiết
async function getDutyStats(filters = {}) {
    try {
        let query = `
            SELECT
                ds.*,
                CASE
                    WHEN ds.total_minutes > 0 THEN CONCAT(
                        FLOOR(ds.total_minutes / 60), 'h ',
                        ds.total_minutes % 60, 'm'
                    )
                    ELSE '0h 0m'
                END as formatted_time,
                CASE
                    WHEN ds.total_sessions > 0 THEN ROUND(ds.total_minutes / ds.total_sessions, 1)
                    ELSE 0
                END as avg_session_minutes
            FROM duty_statistics ds
            WHERE 1=1
        `;
        let params = [];

        if (filters.identifier) {
            query += ' AND ds.identifier = ?';
            params.push(filters.identifier);
        }

        if (filters.job) {
            query += ' AND ds.job = ?';
            params.push(filters.job);
        }

        query += ' ORDER BY ds.total_hours DESC';

        const [rows] = await pool.execute(query, params);
        return rows;
    } catch (error) {
        console.error('Lỗi get duty stats:', error);
        throw error;
    }
}

// Lấy thống kê chi tiết theo ngày
async function getDailyDutyStats(filters = {}) {
    try {
        let query = `
            SELECT
                DATE(dl.timestamp) as duty_date,
                dl.identifier,
                dl.name,
                dl.job,
                COUNT(*) as total_actions,
                SUM(CASE WHEN dl.status = 'on_duty' THEN 1 ELSE 0 END) as on_duty_count,
                SUM(CASE WHEN dl.status = 'off_duty' THEN 1 ELSE 0 END) as off_duty_count,
                MIN(CASE WHEN dl.status = 'on_duty' THEN dl.timestamp END) as first_on_duty,
                MAX(CASE WHEN dl.status = 'off_duty' THEN dl.timestamp END) as last_off_duty
            FROM duty_logs dl
            WHERE 1=1
        `;
        let params = [];

        if (filters.date) {
            query += ' AND DATE(dl.timestamp) = ?';
            params.push(filters.date);
        }

        if (filters.identifier) {
            query += ' AND dl.identifier = ?';
            params.push(filters.identifier);
        }

        if (filters.job) {
            query += ' AND dl.job = ?';
            params.push(filters.job);
        }

        query += ' GROUP BY DATE(dl.timestamp), dl.identifier, dl.job ORDER BY duty_date DESC, dl.name';

        const [rows] = await pool.execute(query, params);
        return rows;
    } catch (error) {
        console.error('Lỗi get daily duty stats:', error);
        throw error;
    }
}

// Lấy sessions chi tiết với thời gian cụ thể
async function getDutySessions(filters = {}) {
    try {
        let query = `
            SELECT
                ds.*,
                CASE
                    WHEN ds.duration_minutes > 0 THEN CONCAT(
                        FLOOR(ds.duration_minutes / 60), 'h ',
                        ds.duration_minutes % 60, 'm'
                    )
                    ELSE 'Đang hoạt động'
                END as formatted_duration,
                DATE(ds.start_time) as session_date
            FROM duty_sessions ds
            WHERE 1=1
        `;
        let params = [];

        if (filters.identifier) {
            query += ' AND ds.identifier = ?';
            params.push(filters.identifier);
        }

        if (filters.job) {
            query += ' AND ds.job = ?';
            params.push(filters.job);
        }

        if (filters.date) {
            query += ' AND DATE(ds.start_time) = ?';
            params.push(filters.date);
        }

        if (filters.status) {
            query += ' AND ds.status = ?';
            params.push(filters.status);
        }

        query += ' ORDER BY ds.start_time DESC';

        if (filters.limit) {
            query += ' LIMIT ?';
            params.push(parseInt(filters.limit));
        }

        const [rows] = await pool.execute(query, params);
        return rows;
    } catch (error) {
        console.error('Lỗi get duty sessions:', error);
        throw error;
    }
}

// Lấy thống kê tổng quan
async function getOverallStats(filters = {}) {
    try {
        let dateFilter = '';
        let params = [];

        if (filters.date) {
            dateFilter = 'AND DATE(timestamp) = ?';
            params.push(filters.date);
        }

        if (filters.job) {
            dateFilter += ' AND job = ?';
            params.push(filters.job);
        }

        const [totalStats] = await pool.execute(
            `SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'on_duty' THEN 1 ELSE 0 END) as onDuty,
                SUM(CASE WHEN status = 'off_duty' THEN 1 ELSE 0 END) as offDuty
             FROM duty_logs WHERE 1=1 ${dateFilter}`,
            params
        );

        const [jobStats] = await pool.execute(
            `SELECT job, COUNT(*) as count FROM duty_logs WHERE 1=1 ${dateFilter} GROUP BY job`,
            params
        );

        const [recentActivity] = await pool.execute(
            `SELECT * FROM duty_logs WHERE 1=1 ${dateFilter} ORDER BY timestamp DESC LIMIT 10`,
            params
        );

        return {
            ...totalStats[0],
            byJob: jobStats.reduce((acc, item) => {
                acc[item.job] = { total: item.count };
                return acc;
            }, {}),
            recentActivity
        };
    } catch (error) {
        console.error('Lỗi get overall stats:', error);
        throw error;
    }
}

// Xử lý player disconnect - tự động off duty
async function handlePlayerDisconnect(identifier, name, reason = 'disconnect') {
    try {
        const disconnectTime = new Date().toISOString();

        // Tìm tất cả sessions đang active của player
        const [activeSessions] = await pool.execute(
            'SELECT * FROM duty_sessions WHERE identifier = ? AND status = "active"',
            [identifier]
        );

        if (activeSessions.length === 0) {
            return {
                message: 'Không có session active nào để kết thúc',
                sessionsEnded: 0
            };
        }

        let sessionsEnded = 0;
        let logsCreated = 0;

        for (const session of activeSessions) {
            // Kết thúc session
            await pool.execute(
                `UPDATE duty_sessions
                 SET end_time = ?, duration_minutes = TIMESTAMPDIFF(MINUTE, start_time, ?), status = 'completed'
                 WHERE id = ?`,
                [disconnectTime, disconnectTime, session.id]
            );

            // Tạo duty log off_duty tự động
            await pool.execute(
                'INSERT INTO duty_logs (identifier, name, job, grade, status, timestamp) VALUES (?, ?, ?, ?, ?, ?)',
                [identifier, name || session.name, session.job, session.grade, 'off_duty', disconnectTime]
            );

            // Cập nhật thống kê
            await updateStatistics(identifier, name || session.name, session.job);

            sessionsEnded++;
            logsCreated++;

            console.log(`[Auto Off-Duty] ${session.name} (${session.job}) - Session ended due to ${reason}`);
        }

        return {
            message: `Đã tự động kết thúc ${sessionsEnded} sessions do ${reason}`,
            sessionsEnded,
            logsCreated,
            disconnectTime
        };

    } catch (error) {
        console.error('Lỗi handle player disconnect:', error);
        throw error;
    }
}

// Cleanup các sessions cũ (quá lâu không off duty)
async function cleanupOldSessions(maxHours = 24) {
    try {
        const cutoffTime = new Date();
        cutoffTime.setHours(cutoffTime.getHours() - maxHours);
        const cutoffTimeStr = cutoffTime.toISOString();

        // Tìm sessions cũ vẫn đang active
        const [oldSessions] = await pool.execute(
            'SELECT * FROM duty_sessions WHERE status = "active" AND start_time < ?',
            [cutoffTimeStr]
        );

        if (oldSessions.length === 0) {
            return {
                message: 'Không có sessions cũ nào cần cleanup',
                sessionsEnded: 0
            };
        }

        let sessionsEnded = 0;
        let logsCreated = 0;

        for (const session of oldSessions) {
            // Kết thúc session với thời gian cutoff
            await pool.execute(
                `UPDATE duty_sessions
                 SET end_time = ?, duration_minutes = TIMESTAMPDIFF(MINUTE, start_time, ?), status = 'completed'
                 WHERE id = ?`,
                [cutoffTimeStr, cutoffTimeStr, session.id]
            );

            // Tạo duty log off_duty tự động
            await pool.execute(
                'INSERT INTO duty_logs (identifier, name, job, grade, status, timestamp) VALUES (?, ?, ?, ?, ?, ?)',
                [session.identifier, session.name, session.job, session.grade, 'off_duty', cutoffTimeStr]
            );

            // Cập nhật thống kê
            await updateStatistics(session.identifier, session.name, session.job);

            sessionsEnded++;
            logsCreated++;

            console.log(`[Cleanup] ${session.name} (${session.job}) - Session ended after ${maxHours}h timeout`);
        }

        return {
            message: `Đã cleanup ${sessionsEnded} sessions cũ`,
            sessionsEnded,
            logsCreated,
            maxHours
        };

    } catch (error) {
        console.error('Lỗi cleanup old sessions:', error);
        throw error;
    }
}

// Lấy danh sách sessions đang active
async function getActiveSessions() {
    try {
        const [rows] = await pool.execute(
            `SELECT
                ds.*,
                TIMESTAMPDIFF(MINUTE, ds.start_time, NOW()) as minutes_active,
                CONCAT(
                    FLOOR(TIMESTAMPDIFF(MINUTE, ds.start_time, NOW()) / 60), 'h ',
                    TIMESTAMPDIFF(MINUTE, ds.start_time, NOW()) % 60, 'm'
                ) as time_active
             FROM duty_sessions ds
             WHERE ds.status = 'active'
             ORDER BY ds.start_time DESC`
        );
        return rows;
    } catch (error) {
        console.error('Lỗi get active sessions:', error);
        throw error;
    }
}

// Kiểm tra trạng thái duty của player khi join server
async function checkPlayerDutyStatus(identifier, name) {
    try {
        // Kiểm tra xem có session active nào không
        const [activeSessions] = await pool.execute(
            'SELECT * FROM duty_sessions WHERE identifier = ? AND status = "active" ORDER BY start_time DESC',
            [identifier]
        );

        // Lấy duty log gần nhất
        const [recentLogs] = await pool.execute(
            'SELECT * FROM duty_logs WHERE identifier = ? ORDER BY timestamp DESC LIMIT 5',
            [identifier]
        );

        // Lấy thống kê player
        const [playerStats] = await pool.execute(
            'SELECT * FROM duty_statistics WHERE identifier = ? ORDER BY total_hours DESC',
            [identifier]
        );

        const result = {
            identifier,
            name,
            hasActiveSessions: activeSessions.length > 0,
            activeSessions: activeSessions,
            recentLogs: recentLogs,
            playerStats: playerStats,
            recommendations: []
        };

        // Đưa ra khuyến nghị
        if (activeSessions.length > 0) {
            const session = activeSessions[0];
            const startTime = new Date(session.start_time);
            const now = new Date();
            const hoursActive = Math.floor((now - startTime) / (1000 * 60 * 60));

            result.recommendations.push({
                type: 'active_session',
                message: `Bạn có session ${session.job} đang active từ ${hoursActive}h trước`,
                actions: ['continue', 'end_session']
            });

            if (hoursActive > 12) {
                result.recommendations.push({
                    type: 'long_session',
                    message: 'Session này đã quá lâu, có thể bạn quên off duty?',
                    actions: ['end_session']
                });
            }
        }

        // Kiểm tra pattern bất thường
        if (recentLogs.length >= 2) {
            const lastLog = recentLogs[0];
            if (lastLog.status === 'on_duty') {
                result.recommendations.push({
                    type: 'last_on_duty',
                    message: 'Lần cuối bạn on duty nhưng chưa off duty',
                    actions: ['continue', 'end_and_new']
                });
            }
        }

        return result;

    } catch (error) {
        console.error('Lỗi check player duty status:', error);
        throw error;
    }
}

// Xử lý player reconnect
async function handlePlayerReconnect(identifier, name, action) {
    try {
        const currentTime = new Date().toISOString();

        switch (action) {
            case 'continue':
                // Tiếp tục session cũ - không làm gì cả
                return {
                    action: 'continue',
                    message: 'Tiếp tục session duty cũ',
                    timestamp: currentTime
                };

            case 'end_session':
                // Kết thúc tất cả sessions active
                const endResult = await handlePlayerDisconnect(identifier, name, 'manual_end_on_reconnect');
                return {
                    action: 'end_session',
                    message: 'Đã kết thúc session cũ',
                    ...endResult,
                    timestamp: currentTime
                };

            case 'end_and_new':
                // Kết thúc session cũ và bắt đầu session mới
                await handlePlayerDisconnect(identifier, name, 'end_for_new_session');

                // Lấy thông tin job từ session cũ
                const [lastSession] = await pool.execute(
                    'SELECT * FROM duty_sessions WHERE identifier = ? ORDER BY start_time DESC LIMIT 1',
                    [identifier]
                );

                if (lastSession.length > 0) {
                    const session = lastSession[0];

                    // Tạo session mới
                    await processDutySession(identifier, name, session.job, session.grade, 'on_duty', currentTime);

                    // Tạo duty log
                    await insertDutyLog(identifier, name, session.job, session.grade, 'on_duty', currentTime);

                    return {
                        action: 'end_and_new',
                        message: 'Đã kết thúc session cũ và bắt đầu session mới',
                        newSession: {
                            job: session.job,
                            grade: session.grade,
                            startTime: currentTime
                        },
                        timestamp: currentTime
                    };
                }
                break;

            case 'ignore':
                // Bỏ qua - không làm gì
                return {
                    action: 'ignore',
                    message: 'Bỏ qua session cũ',
                    timestamp: currentTime
                };

            default:
                throw new Error('Action không hợp lệ: ' + action);
        }

    } catch (error) {
        console.error('Lỗi handle player reconnect:', error);
        throw error;
    }
}

// End duty sessions for admin force off duty
async function endDutySessions(identifier, disconnectTime = new Date()) {
    try {
        // Kết thúc tất cả active sessions của player
        const result = await pool.execute(
            'UPDATE duty_sessions SET end_time = ?, status = "ended" WHERE identifier = ? AND status = "active"',
            [disconnectTime, identifier]
        );

        console.log(`[Admin] Ended ${result[0].affectedRows} active sessions for ${identifier}`);

        return {
            sessionsEnded: result[0].affectedRows,
            timestamp: disconnectTime
        };
    } catch (error) {
        console.error('Lỗi end duty sessions:', error);
        throw error;
    }
}

module.exports = {
    pool,
    testConnection,
    initializeTables,
    insertDutyLog,
    processDutySession,
    updateStatistics,
    getDutyLogs,
    getDutyStats,
    getDailyDutyStats,
    getDutySessions,
    getOverallStats,
    handlePlayerDisconnect,
    cleanupOldSessions,
    getActiveSessions,
    checkPlayerDutyStatus,
    handlePlayerReconnect,
    endDutySessions
};
