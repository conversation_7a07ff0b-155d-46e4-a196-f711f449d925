// API Base URL
const API_BASE = 'http://localhost:3000/api';

// Global variables
let allLogs = [];
let allJobs = new Set();
let playerStats = [];
let dutySessions = [];
let activeSessions = [];
let selectedJob = '';

// Job configuration
const JOB_CONFIG = {
    'police': {
        name: '<PERSON><PERSON><PERSON>',
        icon: 'fas fa-shield-alt',
        color: 'job-police',
        description: 'Lực lượng cảnh sát'
    },
    'police2': {
        name: 'Cảnh Sát 2',
        icon: 'fas fa-user-shield',
        color: 'job-police2',
        description: '<PERSON>ự<PERSON> lượng cảnh sát đặc biệt'
    },
    'ambulance': {
        name: '<PERSON> T<PERSON>',
        icon: 'fas fa-ambulance',
        color: 'job-ambulance',
        description: 'Dịch vụ y tế khẩn cấp'
    },
    'mechanic': {
        name: '<PERSON><PERSON><PERSON>',
        icon: 'fas fa-wrench',
        color: 'job-mechanic',
        description: '<PERSON><PERSON><PERSON> vụ sửa chữa xe'
    },
    'tiembanh': {
        name: '<PERSON><PERSON><PERSON><PERSON>',
        icon: 'fas fa-birthday-cake',
        color: 'job-tiembanh',
        description: 'C<PERSON><PERSON> hàng bánh ngọt'
    },
    'army': {
        name: 'Quân Đội',
        icon: 'fas fa-fighter-jet',
        color: 'job-army',
        description: 'Lực lượng quân đội'
    }
};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initializeDateFilters();
    createJobCards();
    loadDutyLogs();
    loadStats();

    // Auto refresh every 30 seconds
    setInterval(() => {
        loadDutyLogs();
        loadStats();
        // Refresh player stats if tab is active
        if (document.getElementById('stats-tab').classList.contains('active')) {
            loadPlayerStats();
        }
    }, 30000);

    // Tab change events
    document.getElementById('stats-tab').addEventListener('click', function() {
        loadPlayerStats();
    });

    document.getElementById('sessions-tab').addEventListener('click', function() {
        loadDutySessions();
    });

    document.getElementById('active-tab').addEventListener('click', function() {
        loadActiveSessions();
    });

    // Date filter change events
    document.getElementById('filterDate').addEventListener('change', updateFilterInfo);
    document.getElementById('filterDateFrom').addEventListener('change', updateFilterInfo);
    document.getElementById('filterDateTo').addEventListener('change', updateFilterInfo);
    document.getElementById('filterJob').addEventListener('change', updateFilterInfo);
    document.getElementById('filterStatus').addEventListener('change', updateFilterInfo);
});

// Initialize date filters
function initializeDateFilters() {
    const today = new Date().toISOString().split('T')[0];

    // Set default to today
    document.getElementById('filterDate').value = today;
    document.getElementById('filterDateTo').value = today;

    // Set "from date" to 7 days ago
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    document.getElementById('filterDateFrom').value = weekAgo.toISOString().split('T')[0];

    updateFilterInfo();
}

// Create job cards
function createJobCards() {
    const jobCardsContainer = document.getElementById('jobCards');

    // Add "All Jobs" card
    const allJobsCard = `
        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
            <div class="card job-card h-100 ${selectedJob === '' ? 'active' : ''}" onclick="selectJob('')">
                <div class="card-body text-center text-white job-default d-flex flex-column justify-content-center">
                    <div class="job-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h6 class="card-title mb-2 fw-bold">Tất cả Jobs</h6>
                    <small class="card-text mb-3 opacity-75">Xem tất cả nghề nghiệp</small>
                    <div class="mt-auto">
                        <span class="badge bg-light text-dark fs-6 px-3 py-2" id="allJobsCount">0</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    let jobCardsHtml = allJobsCard;

    // Add individual job cards
    Object.keys(JOB_CONFIG).forEach(jobKey => {
        const job = JOB_CONFIG[jobKey];
        jobCardsHtml += `
            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                <div class="card job-card h-100 ${selectedJob === jobKey ? 'active' : ''}" onclick="selectJob('${jobKey}')">
                    <div class="card-body text-center text-white ${job.color} d-flex flex-column justify-content-center position-relative">
                        <div class="job-icon">
                            <i class="${job.icon}"></i>
                        </div>
                        <h6 class="card-title mb-2 fw-bold">${job.name}</h6>
                        <small class="card-text mb-3 opacity-75">${job.description}</small>
                        <div class="mt-auto">
                            <span class="badge bg-light text-dark fs-6 px-3 py-2" id="${jobKey}Count">0</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    jobCardsContainer.innerHTML = jobCardsHtml;
}

// Select job
function selectJob(job) {
    selectedJob = job;

    // Update job filter
    document.getElementById('filterJob').value = job;

    // Update active job card
    document.querySelectorAll('.job-card').forEach(card => {
        card.classList.remove('active');
    });

    // Find and activate the selected card
    const cards = document.querySelectorAll('.job-card');
    cards.forEach(card => {
        const onclick = card.getAttribute('onclick');
        if (onclick && onclick.includes(`'${job}'`)) {
            card.classList.add('active');
        }
    });

    // Update filter info and reload data
    updateFilterInfo();
    loadDutyLogs();
    loadStats();

    // Update player stats if tab is active
    if (document.getElementById('stats-tab').classList.contains('active')) {
        loadPlayerStats();
    }

    // Update duty sessions if tab is active
    if (document.getElementById('sessions-tab').classList.contains('active')) {
        loadDutySessions();
    }

    // Update active sessions if tab is active
    if (document.getElementById('active-tab').classList.contains('active')) {
        loadActiveSessions();
    }
}

// Set date range based on preset
function setDateRange(range) {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    // Clear specific date when using range
    document.getElementById('filterDate').value = '';

    switch (range) {
        case 'today':
            document.getElementById('filterDateFrom').value = todayStr;
            document.getElementById('filterDateTo').value = todayStr;
            break;
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            const yesterdayStr = yesterday.toISOString().split('T')[0];
            document.getElementById('filterDateFrom').value = yesterdayStr;
            document.getElementById('filterDateTo').value = yesterdayStr;
            break;
        case 'week':
            const weekAgo = new Date(today);
            weekAgo.setDate(weekAgo.getDate() - 7);
            document.getElementById('filterDateFrom').value = weekAgo.toISOString().split('T')[0];
            document.getElementById('filterDateTo').value = todayStr;
            break;
        case 'month':
            const monthAgo = new Date(today);
            monthAgo.setDate(monthAgo.getDate() - 30);
            document.getElementById('filterDateFrom').value = monthAgo.toISOString().split('T')[0];
            document.getElementById('filterDateTo').value = todayStr;
            break;
        case 'all':
            document.getElementById('filterDateFrom').value = '';
            document.getElementById('filterDateTo').value = '';
            break;
    }

    updateFilterInfo();
    loadDutyLogs();
}

// Get job badge color
function getJobBadgeColor(job) {
    const colorMap = {
        'police': 'bg-primary',
        'police2': 'bg-info',
        'ambulance': 'bg-success',
        'mechanic': 'bg-warning text-dark',
        'tiembanh': 'bg-secondary',
        'army': 'bg-dark'
    };
    return colorMap[job] || 'bg-secondary';
}

// Update filter info display
function updateFilterInfo() {
    const date = document.getElementById('filterDate').value;
    const dateFrom = document.getElementById('filterDateFrom').value;
    const dateTo = document.getElementById('filterDateTo').value;
    const job = document.getElementById('filterJob').value;
    const status = document.getElementById('filterStatus').value;

    let filterText = [];

    if (date) {
        filterText.push(`Ngày: ${new Date(date).toLocaleDateString('vi-VN')}`);
    } else if (dateFrom || dateTo) {
        if (dateFrom && dateTo) {
            filterText.push(`Từ ${new Date(dateFrom).toLocaleDateString('vi-VN')} đến ${new Date(dateTo).toLocaleDateString('vi-VN')}`);
        } else if (dateFrom) {
            filterText.push(`Từ ${new Date(dateFrom).toLocaleDateString('vi-VN')}`);
        } else if (dateTo) {
            filterText.push(`Đến ${new Date(dateTo).toLocaleDateString('vi-VN')}`);
        }
    }

    if (job) filterText.push(`Job: ${job.toUpperCase()}`);
    if (status) filterText.push(`Trạng thái: ${status === 'on_duty' ? 'On Duty' : 'Off Duty'}`);

    const filterInfo = document.getElementById('currentFilter');
    filterInfo.textContent = filterText.length > 0 ? filterText.join(', ') : 'Tất cả dữ liệu';
}

// Load duty logs from API
async function loadDutyLogs() {
    try {
        showLoading(true);

        // Get filter values
        const job = document.getElementById('filterJob').value;
        const status = document.getElementById('filterStatus').value;
        const date = document.getElementById('filterDate').value;
        const dateFrom = document.getElementById('filterDateFrom').value;
        const dateTo = document.getElementById('filterDateTo').value;
        const player = document.getElementById('filterPlayer').value;
        const limit = document.getElementById('filterLimit').value;

        // Build query string
        const params = new URLSearchParams();
        if (job) params.append('job', job);
        if (status) params.append('status', status);
        if (date) params.append('date', date);
        if (dateFrom) params.append('date_from', dateFrom);
        if (dateTo) params.append('date_to', dateTo);
        if (player) params.append('player', player);
        if (limit) params.append('limit', limit);

        const response = await fetch(`${API_BASE}/fivem/duty-logs?${params}`);
        const data = await response.json();

        if (data.success) {
            allLogs = data.data;
            displayDutyLogs(allLogs);
            updateJobFilter();
            updateJobCounts();
            updateLastUpdateTime();
            updateFilterInfo();
        } else {
            showError('Lỗi khi tải duty logs: ' + data.message);
        }
    } catch (error) {
        console.error('Error loading duty logs:', error);
        showError('Lỗi kết nối đến server');
    } finally {
        showLoading(false);
    }
}

// Load statistics
async function loadStats() {
    try {
        const job = document.getElementById('filterJob').value;
        const date = document.getElementById('filterDate').value;

        const params = new URLSearchParams();
        if (job) params.append('job', job);
        if (date) params.append('date', date);

        const response = await fetch(`${API_BASE}/fivem/duty-stats?${params}`);
        const data = await response.json();

        if (data.success) {
            updateStatistics(data.data);
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

// Load player statistics
async function loadPlayerStats() {
    try {
        showLoadingStats(true);

        const job = document.getElementById('filterJob').value;

        const params = new URLSearchParams();
        if (job) params.append('job', job);

        const response = await fetch(`${API_BASE}/fivem/player-stats?${params}`);
        const data = await response.json();

        if (data.success) {
            playerStats = data.data;
            displayPlayerStats(playerStats);
        } else {
            showError('Lỗi khi tải player stats: ' + data.message);
        }
    } catch (error) {
        console.error('Error loading player stats:', error);
        showError('Lỗi kết nối đến server');
    } finally {
        showLoadingStats(false);
    }
}

// Load duty sessions
async function loadDutySessions() {
    try {
        showLoadingSessions(true);

        const job = document.getElementById('filterJob').value;
        const date = document.getElementById('filterDate').value;
        const dateFrom = document.getElementById('filterDateFrom').value;
        const dateTo = document.getElementById('filterDateTo').value;

        const params = new URLSearchParams();
        if (job) params.append('job', job);
        if (date) params.append('date', date);
        if (dateFrom) params.append('date_from', dateFrom);
        if (dateTo) params.append('date_to', dateTo);
        params.append('limit', '100'); // Limit to 100 sessions

        const response = await fetch(`${API_BASE}/fivem/duty-sessions?${params}`);
        const data = await response.json();

        if (data.success) {
            dutySessions = data.data;
            displayDutySessions(dutySessions);
        } else {
            showError('Lỗi khi tải duty sessions: ' + data.message);
        }
    } catch (error) {
        console.error('Error loading duty sessions:', error);
        showError('Lỗi kết nối đến server');
    } finally {
        showLoadingSessions(false);
    }
}

// Load active sessions
async function loadActiveSessions() {
    try {
        showLoadingActive(true);

        const response = await fetch(`${API_BASE}/fivem/active-sessions`);
        const data = await response.json();

        if (data.success) {
            activeSessions = data.data;
            displayActiveSessions(activeSessions);
        } else {
            showError('Lỗi khi tải active sessions: ' + data.message);
        }
    } catch (error) {
        console.error('Error loading active sessions:', error);
        showError('Lỗi kết nối đến server');
    } finally {
        showLoadingActive(false);
    }
}

// Display duty logs in table
function displayDutyLogs(logs) {
    const tbody = document.getElementById('dutyLogsTable');
    
    if (logs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-4">
                    <i class="fas fa-info-circle me-2"></i>
                    Không có dữ liệu phù hợp với bộ lọc.
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = logs.map(log => {
        const timestamp = new Date(log.timestamp);
        const formattedTime = timestamp.toLocaleString('vi-VN');
        const statusBadge = log.status === 'on_duty' ? 'bg-success' : 'bg-danger';
        const statusText = log.status === 'on_duty' ? '🟢 On Duty' : '🔴 Off Duty';
        const statusIcon = log.status === 'on_duty' ? 'fa-user-check' : 'fa-user-times';
        
        // Job badge color and name
        const jobConfig = JOB_CONFIG[log.job];
        const jobBadgeColor = getJobBadgeColor(log.job);
        const jobDisplayName = jobConfig ? jobConfig.name : log.job.toUpperCase();
        const jobIcon = jobConfig ? jobConfig.icon : 'fas fa-briefcase';
        
        return `
            <tr>
                <td>
                    <small class="text-muted">${formattedTime}</small>
                </td>
                <td>
                    <strong>${log.name}</strong>
                </td>
                <td>
                    <span class="badge ${jobBadgeColor} job-badge">
                        <i class="${jobIcon} me-1"></i>
                        ${jobDisplayName}
                    </span>
                </td>
                <td>
                    <span class="badge bg-secondary">${log.grade}</span>
                </td>
                <td>
                    <span class="badge ${statusBadge}">
                        <i class="fas ${statusIcon} me-1"></i>
                        ${statusText}
                    </span>
                </td>
                <td>
                    <small class="text-muted font-monospace">${log.identifier}</small>
                </td>
            </tr>
        `;
    }).join('');
}

// Display player statistics in table
function displayPlayerStats(stats) {
    const tbody = document.getElementById('playerStatsTable');

    if (stats.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="fas fa-info-circle me-2"></i>
                    Không có dữ liệu thống kê player.
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = stats.map(stat => {
        const totalHours = parseFloat(stat.total_hours || 0);
        const totalSessions = parseInt(stat.total_sessions || 0);
        const avgMinutes = parseFloat(stat.avg_session_minutes || 0);
        const formattedTime = stat.formatted_time || '0h 0m';

        const lastOnDuty = stat.last_on_duty ?
            new Date(stat.last_on_duty).toLocaleString('vi-VN') : 'Chưa có';
        const lastOffDuty = stat.last_off_duty ?
            new Date(stat.last_off_duty).toLocaleString('vi-VN') : 'Chưa có';

        // Job badge color and name
        const jobConfig = JOB_CONFIG[stat.job];
        const jobBadgeColor = getJobBadgeColor(stat.job);
        const jobDisplayName = jobConfig ? jobConfig.name : stat.job.toUpperCase();
        const jobIcon = jobConfig ? jobConfig.icon : 'fas fa-briefcase';

        return `
            <tr>
                <td>
                    <strong>${stat.name}</strong>
                    <br>
                    <small class="text-muted font-monospace">${stat.identifier}</small>
                </td>
                <td>
                    <span class="badge ${jobBadgeColor} job-badge">
                        <i class="${jobIcon} me-1"></i>
                        ${jobDisplayName}
                    </span>
                </td>
                <td>
                    <span class="badge bg-info">${totalSessions}</span>
                </td>
                <td>
                    <strong>${totalHours.toFixed(1)} giờ</strong>
                    <br>
                    <small class="text-muted">${stat.total_minutes} phút</small>
                </td>
                <td>
                    <span class="badge bg-success">${formattedTime}</span>
                </td>
                <td>
                    <span class="badge bg-secondary">${avgMinutes.toFixed(1)} phút</span>
                </td>
                <td>
                    <small class="text-muted">${lastOnDuty}</small>
                </td>
                <td>
                    <small class="text-muted">${lastOffDuty}</small>
                </td>
            </tr>
        `;
    }).join('');
}

// Display duty sessions in table
function displayDutySessions(sessions) {
    const tbody = document.getElementById('dutySessionsTable');

    if (sessions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="fas fa-info-circle me-2"></i>
                    Không có dữ liệu phiên duty.
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = sessions.map(session => {
        const startTime = new Date(session.start_time).toLocaleString('vi-VN');
        const endTime = session.end_time ?
            new Date(session.end_time).toLocaleString('vi-VN') : 'Đang hoạt động';
        const duration = session.formatted_duration || 'Đang hoạt động';
        const sessionDate = new Date(session.start_time).toLocaleDateString('vi-VN');

        // Job badge color and name
        const jobConfig = JOB_CONFIG[session.job];
        const jobBadgeColor = getJobBadgeColor(session.job);
        const jobDisplayName = jobConfig ? jobConfig.name : session.job.toUpperCase();
        const jobIcon = jobConfig ? jobConfig.icon : 'fas fa-briefcase';

        // Status badge
        const statusBadge = session.status === 'active' ?
            '<span class="badge bg-success"><i class="fas fa-play me-1"></i>Đang hoạt động</span>' :
            '<span class="badge bg-secondary"><i class="fas fa-stop me-1"></i>Đã kết thúc</span>';

        // Duration badge color
        const durationBadgeColor = session.status === 'active' ? 'bg-warning' : 'bg-primary';

        return `
            <tr>
                <td>
                    <strong>${session.name}</strong>
                    <br>
                    <small class="text-muted font-monospace">${session.identifier}</small>
                </td>
                <td>
                    <span class="badge ${jobBadgeColor} job-badge">
                        <i class="${jobIcon} me-1"></i>
                        ${jobDisplayName}
                    </span>
                </td>
                <td>
                    <span class="badge bg-info">${session.grade}</span>
                </td>
                <td>
                    <small class="text-success">
                        <i class="fas fa-play me-1"></i>
                        ${startTime}
                    </small>
                </td>
                <td>
                    <small class="text-danger">
                        <i class="fas fa-stop me-1"></i>
                        ${endTime}
                    </small>
                </td>
                <td>
                    <span class="badge ${durationBadgeColor}">
                        <i class="fas fa-clock me-1"></i>
                        ${duration}
                    </span>
                </td>
                <td>
                    ${statusBadge}
                </td>
                <td>
                    <small class="text-muted">${sessionDate}</small>
                </td>
            </tr>
        `;
    }).join('');
}

// Display active sessions in table
function displayActiveSessions(sessions) {
    const tbody = document.getElementById('activeSessionsTable');

    if (sessions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="fas fa-check-circle me-2 text-success"></i>
                    Không có ai đang online duty. Tất cả đã off duty đúng cách!
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = sessions.map(session => {
        const startTime = new Date(session.start_time).toLocaleString('vi-VN');
        const timeActive = session.time_active || 'Đang tính...';
        const minutesActive = session.minutes_active || 0;

        // Job badge color and name
        const jobConfig = JOB_CONFIG[session.job];
        const jobBadgeColor = getJobBadgeColor(session.job);
        const jobDisplayName = jobConfig ? jobConfig.name : session.job.toUpperCase();
        const jobIcon = jobConfig ? jobConfig.icon : 'fas fa-briefcase';

        // Warning for long sessions (over 12 hours)
        const isLongSession = minutesActive > 720; // 12 hours
        const timeActiveClass = isLongSession ? 'text-warning fw-bold' : 'text-info';
        const warningIcon = isLongSession ? '<i class="fas fa-exclamation-triangle text-warning me-1"></i>' : '';

        return `
            <tr ${isLongSession ? 'class="table-warning"' : ''}>
                <td>
                    <strong>${session.name}</strong>
                    <br>
                    <small class="text-muted font-monospace">${session.identifier}</small>
                </td>
                <td>
                    <span class="badge ${jobBadgeColor} job-badge">
                        <i class="${jobIcon} me-1"></i>
                        ${jobDisplayName}
                    </span>
                </td>
                <td>
                    <span class="badge bg-info">${session.grade}</span>
                </td>
                <td>
                    <small class="text-success">
                        <i class="fas fa-play me-1"></i>
                        ${startTime}
                    </small>
                </td>
                <td>
                    <span class="badge bg-success ${timeActiveClass}">
                        ${warningIcon}
                        <i class="fas fa-clock me-1"></i>
                        ${timeActive}
                    </span>
                </td>
                <td>
                    <span class="badge bg-success">
                        <i class="fas fa-circle me-1"></i>
                        Đang Online
                    </span>
                </td>
                <td>
                    <span class="badge bg-info">
                        <i class="fas fa-info-circle me-1"></i>
                        Tự động off khi disconnect
                    </span>
                </td>
            </tr>
        `;
    }).join('');
}

// Update statistics cards
function updateStatistics(stats) {
    const totalLogsEl = document.getElementById('totalLogs');
    const onDutyCountEl = document.getElementById('onDutyCount');
    const offDutyCountEl = document.getElementById('offDutyCount');
    const jobCountEl = document.getElementById('jobCount');

    if (totalLogsEl) totalLogsEl.textContent = stats.total || 0;
    if (onDutyCountEl) onDutyCountEl.textContent = stats.onDuty || 0;
    if (offDutyCountEl) offDutyCountEl.textContent = stats.offDuty || 0;
    if (jobCountEl) jobCountEl.textContent = Object.keys(stats.byJob || {}).length;
}

// Update job filter dropdown
function updateJobFilter() {
    const jobFilter = document.getElementById('filterJob');
    const currentValue = jobFilter.value;

    // Collect all unique jobs
    allLogs.forEach(log => allJobs.add(log.job));

    // Clear and rebuild options
    jobFilter.innerHTML = '<option value="">Tất cả Jobs</option>';

    Array.from(allJobs).sort().forEach(job => {
        const option = document.createElement('option');
        option.value = job;
        const jobConfig = JOB_CONFIG[job];
        option.textContent = jobConfig ? jobConfig.name : job.toUpperCase();
        jobFilter.appendChild(option);
    });

    // Restore previous selection
    jobFilter.value = currentValue;
}

// Update job counts in cards
function updateJobCounts() {
    // Count logs by job
    const jobCounts = {};
    let totalCount = 0;

    allLogs.forEach(log => {
        jobCounts[log.job] = (jobCounts[log.job] || 0) + 1;
        totalCount++;
    });

    // Update all jobs count
    const allJobsCountElement = document.getElementById('allJobsCount');
    if (allJobsCountElement) {
        allJobsCountElement.textContent = totalCount;
    }

    // Update individual job counts
    Object.keys(JOB_CONFIG).forEach(jobKey => {
        const countElement = document.getElementById(jobKey + 'Count');
        if (countElement) {
            countElement.textContent = jobCounts[jobKey] || 0;
        }
    });

    // Update active jobs count in statistics
    const activeJobsCount = Object.keys(jobCounts).length;
    const activeJobsElement = document.getElementById('activeJobs');
    if (activeJobsElement) {
        activeJobsElement.textContent = activeJobsCount;
    }
}

// Clear all filters
function clearFilters() {
    document.getElementById('filterJob').value = '';
    document.getElementById('filterStatus').value = '';
    document.getElementById('filterDate').value = '';
    document.getElementById('filterDateFrom').value = '';
    document.getElementById('filterDateTo').value = '';
    document.getElementById('filterPlayer').value = '';
    document.getElementById('filterLimit').value = '';

    // Reset job selection
    selectedJob = '';
    selectJob('');

    // Reset to default (today)
    initializeDateFilters();
    loadDutyLogs();
}

// Export data to CSV
function exportData() {
    if (allLogs.length === 0) {
        showError('Không có dữ liệu để xuất');
        return;
    }

    // Prepare CSV data
    const headers = ['Thời gian', 'Tên', 'Identifier', 'Job', 'Grade', 'Trạng thái'];
    const csvData = [headers];

    allLogs.forEach(log => {
        const row = [
            new Date(log.timestamp).toLocaleString('vi-VN'),
            log.name,
            log.identifier,
            log.job,
            log.grade,
            log.status === 'on_duty' ? 'On Duty' : 'Off Duty'
        ];
        csvData.push(row);
    });

    // Convert to CSV string
    const csvString = csvData.map(row =>
        row.map(field => `"${field}"`).join(',')
    ).join('\n');

    // Create and download file
    const blob = new Blob(['\uFEFF' + csvString], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `duty_logs_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success message
    showSuccess('Đã xuất dữ liệu thành công!');
}

// Show/hide loading indicator
function showLoading(show) {
    const loading = document.querySelector('.loading');
    loading.style.display = show ? 'block' : 'none';
}

// Show/hide loading indicator for stats
function showLoadingStats(show) {
    const loading = document.querySelector('.loading-stats');
    if (loading) {
        loading.style.display = show ? 'block' : 'none';
    }
}

// Show/hide loading indicator for sessions
function showLoadingSessions(show) {
    const loading = document.querySelector('.loading-sessions');
    if (loading) {
        loading.style.display = show ? 'block' : 'none';
    }
}

// Show/hide loading indicator for active sessions
function showLoadingActive(show) {
    const loading = document.querySelector('.loading-active');
    if (loading) {
        loading.style.display = show ? 'block' : 'none';
    }
}

// Force off duty function (placeholder for compatibility)
function forceOffDuty(identifier, playerName) {
    console.warn('forceOffDuty function has been removed. Use Admin Panel instead.');
    showError('Tính năng Force Off Duty đã được chuyển sang Admin Panel. Vui lòng sử dụng tab Admin.');
}

// Cleanup old sessions
async function cleanupOldSessions() {
    showError('Tính năng cleanup sessions hiện chưa khả dụng. Vui lòng liên hệ admin.');
    console.warn('cleanupOldSessions function is not implemented on server side');
}

// Show error message
function showError(message) {
    showToast(message, 'danger', 'fas fa-exclamation-triangle');
}

// Show success message
function showSuccess(message) {
    showToast(message, 'success', 'fas fa-check-circle');
}

// Show toast notification
function showToast(message, type, icon) {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0 position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="${icon} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        document.body.removeChild(toast);
    });
}

// Update last update time
function updateLastUpdateTime() {
    const now = new Date();
    const updateTimeEl = document.getElementById('updateTime');
    if (updateTimeEl) {
        updateTimeEl.textContent = now.toLocaleTimeString('vi-VN');
    }
}

// Admin Panel Functions
async function loadAdminPanel() {
    try {
        // Load active sessions for admin panel
        const response = await fetch(`${API_BASE}/fivem/active-sessions`);
        const data = await response.json();

        const tbody = document.getElementById('adminPlayersList');

        if (data.success && data.data && data.data.length > 0) {
            tbody.innerHTML = data.data.map(session => `
                <tr>
                    <td>
                        <strong>${session.name}</strong>
                        <br><small class="text-muted font-monospace">${session.identifier}</small>
                    </td>
                    <td>
                        <span class="badge bg-primary">${session.job}</span>
                        <br><small>Grade: ${session.grade}</small>
                    </td>
                    <td>${session.time_active}</td>
                    <td>
                        <span class="badge bg-success">
                            <i class="fas fa-circle me-1"></i>Online
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-danger btn-sm"
                                onclick="forceOffDutyAdmin('${session.identifier}', '${session.name}')"
                                title="Force off duty">
                            <i class="fas fa-power-off me-1"></i>
                            Force Off
                        </button>
                    </td>
                </tr>
            `).join('');
        } else {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-muted">
                        <i class="fas fa-users-slash me-2"></i>
                        Không có player nào đang online
                    </td>
                </tr>
            `;
        }
    } catch (error) {
        console.error('Error loading admin panel:', error);
        document.getElementById('adminPlayersList').innerHTML = `
            <tr>
                <td colspan="5" class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Lỗi khi tải danh sách players
                </td>
            </tr>
        `;
    }
}

async function forceOffDutyAdmin(identifier, playerName) {
    const adminName = document.getElementById('adminName').value.trim();
    const reason = document.getElementById('adminReason').value.trim();

    if (!adminName) {
        showError('Vui lòng nhập tên admin!');
        document.getElementById('adminName').focus();
        return;
    }

    if (!reason) {
        showError('Vui lòng nhập lý do force off duty!');
        document.getElementById('adminReason').focus();
        return;
    }

    if (!confirm(`Bạn có chắc muốn force off duty cho player "${playerName}"?\n\nLý do: ${reason}`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/fivem/admin/force-off-duty`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                identifier: identifier,
                adminName: adminName,
                reason: reason
            })
        });

        const data = await response.json();

        if (data.success) {
            // Show success message
            showSuccess(`✅ Đã force off duty thành công cho ${data.data.playerName}!`);

            // Add to admin actions log
            addAdminActionLog(data.data);

            // Refresh admin panel and other tabs
            loadAdminPanel();
            loadActiveSessions();
            loadDutyLogs();

            // Clear reason field
            document.getElementById('adminReason').value = '';

        } else {
            showError(`❌ Lỗi: ${data.message}`);
        }

    } catch (error) {
        console.error('Error forcing off duty:', error);
        showError('❌ Lỗi kết nối server khi force off duty!');
    }
}

function addAdminActionLog(actionData) {
    const tbody = document.getElementById('adminActionsLog');

    // Remove "no actions" row if exists
    if (tbody.innerHTML.includes('Chưa có hành động nào')) {
        tbody.innerHTML = '';
    }

    // Add new action to top
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>${new Date(actionData.timestamp).toLocaleString()}</td>
        <td><strong>${actionData.adminName}</strong></td>
        <td>${actionData.playerName} (${actionData.job})</td>
        <td><span class="badge bg-warning">Force Off Duty</span></td>
        <td>${actionData.reason}</td>
    `;

    tbody.insertBefore(newRow, tbody.firstChild);

    // Keep only last 10 actions
    while (tbody.children.length > 10) {
        tbody.removeChild(tbody.lastChild);
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadDutyLogs();
    loadActiveSessions();
    loadDutySessions();
    loadPlayerStats();

    // Load admin panel when tab is clicked
    const adminTab = document.getElementById('admin-tab');
    if (adminTab) {
        adminTab.addEventListener('click', function() {
            loadAdminPanel();
        });
    }

    // Auto refresh every 30 seconds
    setInterval(() => {
        loadDutyLogs();
        loadActiveSessions();
        loadDutySessions();
        loadPlayerStats();

        // Refresh admin panel if it's active
        const adminTab = document.getElementById('admin-tab');
        if (adminTab && adminTab.classList.contains('active')) {
            loadAdminPanel();
        }
    }, 30000);
});

// Export functions for testing
window.dutyDashboard = {
    loadDutyLogs,
    loadStats,
    loadPlayerStats,
    loadDutySessions,
    loadActiveSessions,
    clearFilters,
    setDateRange,
    exportData,
    selectJob,
    createJobCards,
    forceOffDuty,
    cleanupOldSessions,
    loadAdminPanel,
    forceOffDutyAdmin
};
