// API Base URL
const API_BASE = 'http://localhost:3000/api';

// Global variables
let allLogs = [];
let allJobs = new Set();

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadDutyLogs();
    loadStats();
    
    // Auto refresh every 30 seconds
    setInterval(() => {
        loadDutyLogs();
        loadStats();
    }, 30000);
    
    // Set today's date as default
    document.getElementById('filterDate').value = new Date().toISOString().split('T')[0];
});

// Load duty logs from API
async function loadDutyLogs() {
    try {
        showLoading(true);
        
        // Get filter values
        const job = document.getElementById('filterJob').value;
        const status = document.getElementById('filterStatus').value;
        const date = document.getElementById('filterDate').value;
        const limit = document.getElementById('filterLimit').value;
        
        // Build query string
        const params = new URLSearchParams();
        if (job) params.append('job', job);
        if (status) params.append('status', status);
        if (date) params.append('date', date);
        if (limit) params.append('limit', limit);
        
        const response = await fetch(`${API_BASE}/fivem/duty-logs?${params}`);
        const data = await response.json();
        
        if (data.success) {
            allLogs = data.data;
            displayDutyLogs(allLogs);
            updateJobFilter();
            updateLastUpdateTime();
        } else {
            showError('Lỗi khi tải duty logs: ' + data.message);
        }
    } catch (error) {
        console.error('Error loading duty logs:', error);
        showError('Lỗi kết nối đến server');
    } finally {
        showLoading(false);
    }
}

// Load statistics
async function loadStats() {
    try {
        const job = document.getElementById('filterJob').value;
        const date = document.getElementById('filterDate').value;
        
        const params = new URLSearchParams();
        if (job) params.append('job', job);
        if (date) params.append('date', date);
        
        const response = await fetch(`${API_BASE}/fivem/duty-stats?${params}`);
        const data = await response.json();
        
        if (data.success) {
            updateStatistics(data.data);
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

// Display duty logs in table
function displayDutyLogs(logs) {
    const tbody = document.getElementById('dutyLogsTable');
    
    if (logs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-4">
                    <i class="fas fa-info-circle me-2"></i>
                    Không có dữ liệu phù hợp với bộ lọc.
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = logs.map(log => {
        const timestamp = new Date(log.timestamp);
        const formattedTime = timestamp.toLocaleString('vi-VN');
        const statusBadge = log.status === 'on_duty' ? 'badge-on-duty' : 'badge-off-duty';
        const statusText = log.status === 'on_duty' ? 'On Duty' : 'Off Duty';
        const statusIcon = log.status === 'on_duty' ? 'fa-user-check' : 'fa-user-times';
        
        // Job badge color
        const jobColors = {
            'police': 'bg-primary',
            'police2': 'bg-info',
            'ambulance': 'bg-success',
            'mechanic': 'bg-warning',
            'tiembanh': 'bg-secondary'
        };
        const jobBadgeColor = jobColors[log.job] || 'bg-dark';
        
        return `
            <tr>
                <td>
                    <small class="text-muted">${formattedTime}</small>
                </td>
                <td>
                    <strong>${log.name}</strong>
                </td>
                <td>
                    <span class="badge ${jobBadgeColor} job-badge">${log.job.toUpperCase()}</span>
                </td>
                <td>
                    <span class="badge bg-secondary">${log.grade}</span>
                </td>
                <td>
                    <span class="badge ${statusBadge}">
                        <i class="fas ${statusIcon} me-1"></i>
                        ${statusText}
                    </span>
                </td>
                <td>
                    <small class="text-muted font-monospace">${log.identifier}</small>
                </td>
            </tr>
        `;
    }).join('');
}

// Update statistics cards
function updateStatistics(stats) {
    document.getElementById('totalLogs').textContent = stats.total;
    document.getElementById('onDutyCount').textContent = stats.onDuty;
    document.getElementById('offDutyCount').textContent = stats.offDuty;
    document.getElementById('jobCount').textContent = Object.keys(stats.byJob).length;
}

// Update job filter dropdown
function updateJobFilter() {
    const jobFilter = document.getElementById('filterJob');
    const currentValue = jobFilter.value;
    
    // Collect all unique jobs
    allLogs.forEach(log => allJobs.add(log.job));
    
    // Clear and rebuild options
    jobFilter.innerHTML = '<option value="">Tất cả Jobs</option>';
    
    Array.from(allJobs).sort().forEach(job => {
        const option = document.createElement('option');
        option.value = job;
        option.textContent = job.toUpperCase();
        jobFilter.appendChild(option);
    });
    
    // Restore previous selection
    jobFilter.value = currentValue;
}

// Clear all filters
function clearFilters() {
    document.getElementById('filterJob').value = '';
    document.getElementById('filterStatus').value = '';
    document.getElementById('filterDate').value = '';
    document.getElementById('filterLimit').value = '';
    loadDutyLogs();
}

// Show/hide loading indicator
function showLoading(show) {
    const loading = document.querySelector('.loading');
    loading.style.display = show ? 'block' : 'none';
}

// Show error message
function showError(message) {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        document.body.removeChild(toast);
    });
}

// Update last update time
function updateLastUpdateTime() {
    const now = new Date();
    document.getElementById('updateTime').textContent = now.toLocaleTimeString('vi-VN');
}

// Export functions for testing
window.dutyDashboard = {
    loadDutyLogs,
    loadStats,
    clearFilters
};
