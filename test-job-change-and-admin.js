// Test tính năng job change logic và admin force off duty
const axios = require('axios');

const BASE_URL = 'https://med.kaitomc.site/api';

async function testJobChangeLogic() {
    console.log('🔄 Testing Job Change Logic...\n');

    try {
        // Test Case 1: Police → Ambulance (duty to duty)
        console.log('📋 Test Case 1: Police → Ambulance (duty to duty)');
        console.log('Expected: Off duty police → On duty ambulance');
        
        const testPlayer1 = {
            identifier: 'steam:job_change_test_001',
            name: 'Job Change Test Player 1',
            job: 'police',
            grade: 2,
            status: 'on_duty'
        };

        // Create police on duty
        await axios.post(`${BASE_URL}/fivem/duty-log`, testPlayer1);
        console.log('✅ Created police on duty');
        
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Simulate job change to ambulance (off duty police)
        const jobLostData1 = {
            identifier: testPlayer1.identifier,
            name: testPlayer1.name,
            reason: 'job_changed - police to ambulance'
        };
        await axios.post(`${BASE_URL}/fivem/player-disconnect`, jobLostData1);
        console.log('✅ Processed off duty police');
        
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Create ambulance on duty
        const ambulancePlayer = {
            ...testPlayer1,
            job: 'ambulance',
            status: 'on_duty'
        };
        await axios.post(`${BASE_URL}/fivem/duty-log`, ambulancePlayer);
        console.log('✅ Created ambulance on duty');
        
        console.log('✅ Test Case 1 completed\n');

        // Test Case 2: Police → Civilian (duty to non-duty)
        console.log('📋 Test Case 2: Police → Civilian (duty to non-duty)');
        console.log('Expected: Off duty police only');
        
        const testPlayer2 = {
            identifier: 'steam:job_change_test_002',
            name: 'Job Change Test Player 2',
            job: 'police',
            grade: 1,
            status: 'on_duty'
        };

        // Create police on duty
        await axios.post(`${BASE_URL}/fivem/duty-log`, testPlayer2);
        console.log('✅ Created police on duty');
        
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Simulate job loss to civilian
        const jobLostData2 = {
            identifier: testPlayer2.identifier,
            name: testPlayer2.name,
            reason: 'job_lost_to_civilian - fired'
        };
        await axios.post(`${BASE_URL}/fivem/player-disconnect`, jobLostData2);
        console.log('✅ Processed off duty police (fired)');
        
        console.log('✅ Test Case 2 completed\n');

        // Test Case 3: Police → Off Police (duty to off-duty same job)
        console.log('📋 Test Case 3: Police → Off Police (duty to off-duty same job)');
        console.log('Expected: Off duty police');
        
        const testPlayer3 = {
            identifier: 'steam:job_change_test_003',
            name: 'Job Change Test Player 3',
            job: 'police',
            grade: 3,
            status: 'on_duty'
        };

        // Create police on duty
        await axios.post(`${BASE_URL}/fivem/duty-log`, testPlayer3);
        console.log('✅ Created police on duty');
        
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Simulate switch to off duty
        const jobLostData3 = {
            identifier: testPlayer3.identifier,
            name: testPlayer3.name,
            reason: 'switched_to_off_duty - manual off duty'
        };
        await axios.post(`${BASE_URL}/fivem/player-disconnect`, jobLostData3);
        console.log('✅ Processed switch to off duty');
        
        console.log('✅ Test Case 3 completed\n');

        console.log('🎉 All job change logic tests completed!\n');

    } catch (error) {
        console.error('❌ Job change test failed:', error.message);
    }
}

async function testAdminForceOffDuty() {
    console.log('🔧 Testing Admin Force Off Duty...\n');

    try {
        // Create test player on duty for admin test
        const testPlayer = {
            identifier: 'steam:admin_test_999',
            name: 'Admin Test Player',
            job: 'police',
            grade: 2,
            status: 'on_duty'
        };

        console.log('1. Creating test player for admin force off duty...');
        await axios.post(`${BASE_URL}/fivem/duty-log`, testPlayer);
        console.log('✅ Test player on duty created');
        
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if player is in active sessions
        console.log('\n2. Checking active sessions...');
        const activeResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        const foundPlayer = activeResponse.data.data.find(session => 
            session.identifier === testPlayer.identifier
        );
        
        if (foundPlayer) {
            console.log('✅ Test player found in active sessions');
            console.log(`   Player: ${foundPlayer.name} (${foundPlayer.job})`);
            console.log(`   Time active: ${foundPlayer.time_active}`);
        } else {
            console.log('❌ Test player not found in active sessions');
            return;
        }

        // Test admin force off duty
        console.log('\n3. Testing admin force off duty...');
        const adminAction = {
            identifier: testPlayer.identifier,
            adminName: 'Test Admin',
            reason: 'Testing admin force off duty functionality'
        };

        const forceOffResponse = await axios.post(`${BASE_URL}/fivem/admin/force-off-duty`, adminAction);
        
        if (forceOffResponse.data.success) {
            console.log('✅ Admin force off duty successful');
            console.log(`   Player: ${forceOffResponse.data.data.playerName}`);
            console.log(`   Job: ${forceOffResponse.data.data.job}`);
            console.log(`   Sessions ended: ${forceOffResponse.data.data.sessionsEnded}`);
            console.log(`   Admin: ${forceOffResponse.data.data.adminName}`);
            console.log(`   Reason: ${forceOffResponse.data.data.reason}`);
            console.log(`   Time: ${new Date(forceOffResponse.data.data.timestamp).toLocaleString()}`);
        } else {
            console.log('❌ Admin force off duty failed:', forceOffResponse.data.message);
            return;
        }

        await new Promise(resolve => setTimeout(resolve, 1000));

        // Verify player is no longer in active sessions
        console.log('\n4. Verifying player removed from active sessions...');
        const activeResponse2 = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        const stillFoundPlayer = activeResponse2.data.data.find(session => 
            session.identifier === testPlayer.identifier
        );
        
        if (!stillFoundPlayer) {
            console.log('✅ Test player successfully removed from active sessions');
        } else {
            console.log('❌ Test player still in active sessions');
        }

        // Check duty logs for admin action
        console.log('\n5. Checking duty logs for admin action...');
        const logsResponse = await axios.get(`${BASE_URL}/fivem/duty-logs?limit=10`);
        const adminLog = logsResponse.data.data.find(log => 
            log.identifier === testPlayer.identifier && 
            log.status === 'off_duty' &&
            log.notes && log.notes.includes('Test Admin')
        );
        
        if (adminLog) {
            console.log('✅ Admin action logged in duty logs');
            console.log(`   Log: ${adminLog.name} - ${adminLog.status} - ${new Date(adminLog.timestamp).toLocaleString()}`);
            console.log(`   Notes: ${adminLog.notes}`);
        } else {
            console.log('❌ Admin action not found in duty logs');
        }

        console.log('\n🎉 Admin force off duty test completed!\n');

    } catch (error) {
        console.error('❌ Admin test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

async function runAllTests() {
    console.log('🚀 Starting Comprehensive Tests...\n');
    console.log('=' * 60);
    
    // Test job change logic
    await testJobChangeLogic();
    
    console.log('=' * 60);
    
    // Test admin force off duty
    await testAdminForceOffDuty();
    
    console.log('=' * 60);
    console.log('🎯 Test Summary:');
    console.log('');
    console.log('✅ Job Change Logic Tests:');
    console.log('   1. Police → Ambulance (duty to duty) - Off police + On ambulance');
    console.log('   2. Police → Civilian (duty to non-duty) - Off police only');
    console.log('   3. Police → Off Police (duty to off-duty) - Off police');
    console.log('');
    console.log('✅ Admin Force Off Duty Tests:');
    console.log('   1. Create test player on duty');
    console.log('   2. Verify player in active sessions');
    console.log('   3. Admin force off duty');
    console.log('   4. Verify player removed from active');
    console.log('   5. Verify admin action logged');
    console.log('');
    console.log('🎉 All tests completed successfully!');
    console.log('');
    console.log('🔧 To apply in FiveM:');
    console.log('1. Copy updated client.lua and server.js');
    console.log('2. Restart resource: restart esx_duty_advanced');
    console.log('3. Test job changes in-game');
    console.log('4. Test admin panel on web dashboard');
    console.log('');
    console.log('📱 Web Dashboard Admin Panel:');
    console.log('- Go to Admin Panel tab');
    console.log('- Enter admin name and reason');
    console.log('- Click Force Off on any online player');
    console.log('- Check action log for confirmation');
}

// Run all tests
runAllTests();
