local Keys = {
  ["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
  ["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
  ["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
  ["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
  ["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
  ["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
  ["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
  ["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
}

--- action functions
local CurrentAction           = nil
local CurrentActionMsg        = ''
local CurrentActionData       = {}
local HasAlreadyEnteredMarker = false
local LastZone                = nil

--- esx
local GUI = {}
GUI.Time                      = 0
local PlayerData              = {}

-- Biến để theo dõi trạng thái duty
local isOnDuty                = false
local lastNotificationTime    = 0
local hasCheckedDutyStatus    = false
local isReconnectMenuOpen     = false

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
  PlayerData = xPlayer
  -- Khởi tạo trạng thái duty ban đầu (có thể lấy từ server nếu cần)
  isOnDuty = false
  
  -- Delay để đảm bảo player đã load hoàn toàn
  Citizen.SetTimeout(5000, function()
    if not hasCheckedDutyStatus then
      -- Kiểm tra duty status khi player join
      TriggerServerEvent('duty:checkStatusOnJoin')
      hasCheckedDutyStatus = true
    end
  end)
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
  PlayerData.job = job
  -- Reset trạng thái duty khi thay đổi job
  isOnDuty = false
end)

---- markers
AddEventHandler('esx_duty:hasEnteredMarker', function(zone)
  if zone ~= nil then
      CurrentAction     = 'onoff'
      CurrentActionMsg  = _U('duty') .. (isOnDuty and ' (Tắt Duty)' or ' (Bật Duty)')
      -- Chỉ hiển thị thông báo dopeNotify
      if exports['dopeNotify'] then
        exports['dopeNotify']:ShowNotification('info', 'Nhấn [E] để ' .. (isOnDuty and 'TẮT' or 'BẬT') .. ' Duty', 3000)
      end
  end
end)

AddEventHandler('esx_duty:hasExitedMarker', function(zone)
  CurrentAction = nil
  -- Bỏ thông báo khi rời khỏi marker
end)

-- keycontrols
Citizen.CreateThread(function()
  while true do
      Citizen.Wait(1)

      local playerPed = GetPlayerPed(-1)
      
      local jobs = {
          'offambulance',
          'offpolice',
          'offmechanic',
          'offpolice2',
          'offtiembanh',
          'police',
          'ambulance',
          'mechanic',
          'police2',
          'tiembanh'
      }

      if CurrentAction ~= nil and not isReconnectMenuOpen then
          for k, v in pairs(jobs) do
              if PlayerData.job and PlayerData.job.name == v then
                  if IsControlJustPressed(0, Keys['E']) then
                      -- Chuyển đổi trạng thái duty
                      isOnDuty = not isOnDuty
                      local status = isOnDuty and 'on_duty' or 'off_duty'
                      TriggerServerEvent('duty:onoff', status)
                  end
              end
          end
      end
  end       
end)

-- Display markers
Citizen.CreateThread(function()
  while true do
      Wait(0)

      if PlayerData.job then
          local coords = GetEntityCoords(GetPlayerPed(-1))

          for k, v in pairs(Config.Zones) do
              -- Kiểm tra xem marker có phù hợp với job hiện tại không
              if ShouldShowMarker(k, PlayerData.job.name) then
                  if(v.Type ~= -1 and GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < Config.DrawDistance) then
                      DrawMarker(v.Type, v.Pos.x, v.Pos.y, v.Pos.z, 0.0, 0.0, 0.0, 0, 0.0, 0.0, v.Size.x, v.Size.y, v.Size.z, v.Color.r, v.Color.g, v.Color.b, 100, true, true, 2, nil, nil, false)
                  end
              end
          end
      end
  end
end)

-- Enter / Exit marker events
Citizen.CreateThread(function()
  while true do
      Wait(0)

      if PlayerData.job then
          local coords      = GetEntityCoords(GetPlayerPed(-1))
          local isInMarker  = false
          local currentZone = nil

          for k, v in pairs(Config.Zones) do
              -- Chỉ kiểm tra marker phù hợp với job
              if ShouldShowMarker(k, PlayerData.job.name) then
                  if(GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < Config.InteractionDistance) then
                      isInMarker  = true
                      currentZone = k
                  end
              end
          end

          if (isInMarker and not HasAlreadyEnteredMarker) or (isInMarker and LastZone ~= currentZone) then
              HasAlreadyEnteredMarker = true
              LastZone                = currentZone
              TriggerEvent('esx_duty:hasEnteredMarker', currentZone)
          end

          if not isInMarker and HasAlreadyEnteredMarker then
              HasAlreadyEnteredMarker = false
              TriggerEvent('esx_duty:hasExitedMarker', LastZone)
          end
      end
  end
end)

-- Nhận thông báo từ server khi duty thay đổi
RegisterNetEvent('duty:statusChanged')
AddEventHandler('duty:statusChanged', function(status, message)
  isOnDuty = (status == 'on_duty')
  
  -- Chỉ giữ lại thông báo trạng thái với kiểm tra thời gian để tránh spam
  if not lastNotificationTime or (GetGameTimer() - lastNotificationTime) > 2000 then
    local statusText = isOnDuty and 'BẬT' or 'TẮT'
    local notificationMessage = message or ('Bạn đã ' .. statusText .. ' Duty thành công!')
    
    if exports['dopeNotify'] then
      exports['dopeNotify']:ShowNotification('success', notificationMessage, 4000)
    else
      -- Fallback notification
      SetNotificationTextEntry("STRING")
      AddTextComponentString(notificationMessage)
      DrawNotification(0, 1)
    end
    
    lastNotificationTime = GetGameTimer()
  end
  
  -- Cập nhật lại thông báo tại marker
  if CurrentAction == 'onoff' then
      CurrentActionMsg = _U('duty') .. (isOnDuty and ' (Tắt Duty)' or ' (Bật Duty)')
  end
end)

-- Nhận event hiển thị menu reconnect
RegisterNetEvent('duty:showReconnectMenu')
AddEventHandler('duty:showReconnectMenu', function(data)
  if isReconnectMenuOpen then return end
  
  isReconnectMenuOpen = true
  
  local message = data.message
  local session = data.session
  local recommendations = data.recommendations
  
  -- Tạo menu options
  local menuOptions = {
      {
          label = "🔄 Tiếp tục session cũ",
          description = "Tiếp tục làm việc từ session trước",
          action = "continue"
      },
      {
          label = "⏹️ Kết thúc session cũ",
          description = "Kết thúc session và về trạng thái off duty",
          action = "end_session"
      },
      {
          label = "🔄⏹️ Kết thúc và bắt đầu mới",
          description = "Kết thúc session cũ và bắt đầu session mới",
          action = "end_and_new"
      },
      {
          label = "❌ Bỏ qua",
          description = "Bỏ qua session cũ (không khuyến khích)",
          action = "ignore"
      }
  }
  
  -- Hiển thị menu
  ShowReconnectNativeMenu(message, menuOptions)
end)

-- Thêm hàm kiểm tra xem marker có phù hợp với job hiện tại không
function ShouldShowMarker(markerName, jobName)
    -- Ánh xạ từ tên marker đến các job được phép thấy marker đó
    local markerJobMap = {
        PoliceDuty = {'police', 'offpolice'},
        AmbulanceDuty = {'ambulance', 'offambulance'},
        MechanicDuty = {'mechanic', 'offmechanic'},
        Police2Duty = {'police2', 'offpolice2'},
        FoodDuty = {'tiembanh', 'offtiembanh'}
    }
    
    -- Kiểm tra xem job hiện tại có trong danh sách job được phép thấy marker không
    local allowedJobs = markerJobMap[markerName]
    if allowedJobs then
        for _, allowedJob in pairs(allowedJobs) do
            if jobName == allowedJob then
                return true
            end
        end
    end
    
    return false
end

-- Hàm hiển thị menu reconnect native
function ShowReconnectNativeMenu(message, options)
    Citizen.CreateThread(function()
        -- Disable controls while menu is open
        local menuActive = true
        local selectedIndex = 1
        local maxIndex = #options

        -- Display instructions
        Citizen.CreateThread(function()
            while menuActive do
                Citizen.Wait(0)

                -- Disable all controls
                DisableAllControlActions(0)
                EnableControlAction(0, 1, true) -- LookLeftRight
                EnableControlAction(0, 2, true) -- LookUpDown
                EnableControlAction(0, 172, true) -- Up Arrow
                EnableControlAction(0, 173, true) -- Down Arrow
                EnableControlAction(0, 176, true) -- Enter
                EnableControlAction(0, 177, true) -- Backspace

                -- Draw background
                DrawRect(0.5, 0.5, 0.65, 0.85, 0, 0, 0, 220)
                DrawRect(0.5, 0.5, 0.63, 0.83, 30, 30, 30, 200)

                -- Draw title
                SetTextFont(4)
                SetTextProportional(1)
                SetTextScale(0.9, 0.9)
                SetTextColour(255, 255, 255, 255)
                SetTextEntry("STRING")
                AddTextComponentString("🔄 DUTY RECONNECT SYSTEM")
                DrawText(0.18, 0.12)

                -- Draw message
                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.42, 0.42)
                SetTextColour(200, 200, 200, 255)
                SetTextEntry("STRING")
                AddTextComponentString(message)
                DrawText(0.18, 0.22)

                -- Draw options
                for i, option in ipairs(options) do
                    local yPos = 0.42 + (i - 1) * 0.09
                    local color = {180, 180, 180, 255}
                    local bgColor = {40, 40, 40, 150}

                    if i == selectedIndex then
                        color = {255, 255, 255, 255}
                        bgColor = {70, 130, 180, 200}
                        -- Highlight effect
                        DrawRect(0.5, yPos, 0.55, 0.07, bgColor[1], bgColor[2], bgColor[3], bgColor[4])
                    else
                        DrawRect(0.5, yPos, 0.55, 0.07, bgColor[1], bgColor[2], bgColor[3], bgColor[4])
                    end

                    -- Draw option text
                    SetTextFont(0)
                    SetTextProportional(1)
                    SetTextScale(0.45, 0.45)
                    SetTextColour(color[1], color[2], color[3], color[4])
                    SetTextEntry("STRING")
                    AddTextComponentString(option.label)
                    DrawText(0.23, yPos - 0.025)

                    -- Draw description
                    SetTextFont(0)
                    SetTextProportional(1)
                    SetTextScale(0.32, 0.32)
                    SetTextColour(color[1], color[2], color[3], 180)
                    SetTextEntry("STRING")
                    AddTextComponentString(option.description)
                    DrawText(0.23, yPos + 0.015)
                end

                -- Draw instructions
                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.35, 0.35)
                SetTextColour(255, 255, 0, 255)
                SetTextEntry("STRING")
                AddTextComponentString("⬆️⬇️ Di chuyển | ENTER Chọn | BACKSPACE Hủy")
                DrawText(0.18, 0.82)

                -- Handle input
                if IsControlJustPressed(0, 172) then -- Up Arrow
                    selectedIndex = selectedIndex - 1
                    if selectedIndex < 1 then
                        selectedIndex = maxIndex
                    end
                    PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                end

                if IsControlJustPressed(0, 173) then -- Down Arrow
                    selectedIndex = selectedIndex + 1
                    if selectedIndex > maxIndex then
                        selectedIndex = 1
                    end
                    PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                end

                if IsControlJustPressed(0, 176) then -- Enter
                    local selectedOption = options[selectedIndex]
                    PlaySoundFrontend(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)

                    -- Send choice to server
                    TriggerServerEvent('duty:handleReconnectChoice', selectedOption.action)

                    menuActive = false
                    isReconnectMenuOpen = false

                    -- Show confirmation
                    if exports['dopeNotify'] then
                        exports['dopeNotify']:ShowNotification('success', 'Đã chọn: ' .. selectedOption.label, 3000)
                    end
                    break
                end

                if IsControlJustPressed(0, 177) then -- Backspace
                    PlaySoundFrontend(-1, "BACK", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)

                    -- Default to ignore
                    TriggerServerEvent('duty:handleReconnectChoice', 'ignore')

                    menuActive = false
                    isReconnectMenuOpen = false

                    if exports['dopeNotify'] then
                        exports['dopeNotify']:ShowNotification('info', 'Đã hủy - bỏ qua session cũ', 3000)
                    end
                    break
                end
            end
        end)
    end)
end

-- Command để test menu (chỉ dành cho admin)
RegisterCommand('testdutymenu', function()
    if PlayerData.job and PlayerData.job.grade >= 5 then -- Chỉ admin
        local testData = {
            message = "🧪 TEST MENU\n\nBạn có session POLICE đang active từ 2 giờ trước.\n\nBạn muốn:",
            session = {
                job = "police",
                start_time = os.time() - 7200 -- 2 hours ago
            },
            recommendations = {}
        }

        TriggerEvent('duty:showReconnectMenu', testData)
    else
        if exports['dopeNotify'] then
            exports['dopeNotify']:ShowNotification('error', 'Bạn không có quyền sử dụng lệnh này!', 3000)
        end
    end
end, false)

-- Export functions for other resources
exports('isOnDuty', function()
    return isOnDuty
end)

exports('isReconnectMenuOpen', function()
    return isReconnectMenuOpen
end)

exports('showReconnectMenu', function(data)
    TriggerEvent('duty:showReconnectMenu', data)
end)
