local Keys = {
  ["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
  ["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
  ["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
  ["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
  ["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
  ["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
  ["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
  ["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
}

--- action functions
local CurrentAction           = nil
local CurrentActionMsg        = ''
local CurrentActionData       = {}
local HasAlreadyEnteredMarker = false
local LastZone                = nil

--- esx
ESX = nil
local GUI = {}
GUI.Time                      = 0
local PlayerData              = {}

-- Biến để theo dõi trạng thái duty
local isOnDuty                = false
local lastNotificationTime    = 0
local hasCheckedDutyStatus    = false
local isReconnectMenuOpen     = false

-- Khởi tạo ESX khi resource start
Citizen.CreateThread(function()
  while ESX == nil do
    TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
    Citizen.Wait(0)
  end

  -- Nếu player đã load rồi (restart resource), lấy data ngay
  if ESX.GetPlayerData() and ESX.GetPlayerData().job then
    PlayerData = ESX.GetPlayerData()
    isOnDuty = IsOnDutyJob(PlayerData.job.name)
    print('[ESX_DUTY] Resource restarted - Player job: ' .. PlayerData.job.name .. ', duty: ' .. tostring(isOnDuty))
  end
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
  PlayerData = xPlayer
  -- Khởi tạo trạng thái duty ban đầu (có thể lấy từ server nếu cần)
  isOnDuty = false

  -- Delay để đảm bảo player đã load hoàn toàn
  Citizen.SetTimeout(5000, function()
    if not hasCheckedDutyStatus then
      -- Kiểm tra xem có phải job off-duty không
      if PlayerData.job and IsOffDutyJob(PlayerData.job.name) then
        -- Hiển thị menu on duty
        ShowOnDutyMenu()
      end
      hasCheckedDutyStatus = true
    end
  end)
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
  local oldJob = PlayerData.job
  PlayerData.job = job

  -- Kiểm tra các trường hợp thay đổi job
  if oldJob and oldJob.name ~= job.name then
    local wasOnDuty = IsOnDutyJob(oldJob.name)
    local isNowOnDuty = IsOnDutyJob(job.name)
    local isNowAnyDuty = IsAnyDutyJob(job.name)

    local playerName = GetPlayerName(PlayerId())
    local identifier = GetPlayerIdentifier()

    if identifier and playerName then
      -- Case 1: Từ on-duty job sang non-duty job (police → civilian)
      if wasOnDuty and not isNowAnyDuty then
        TriggerServerEvent('duty:jobLost', identifier, playerName, oldJob.name, oldJob.grade, 'job_lost_to_civilian')

        if exports['dopeNotify'] then
          exports['dopeNotify']:ShowNotification('warning', '⚠️ Bạn đã mất job duty - Tự động off duty trên hệ thống', 5000)
        end

      -- Case 2: Từ on-duty job sang on-duty job khác (police → ambulance)
      elseif wasOnDuty and isNowOnDuty then
        -- Off duty job cũ trước
        TriggerServerEvent('duty:jobLost', identifier, playerName, oldJob.name, oldJob.grade, 'job_changed')

        -- Delay nhỏ rồi on duty job mới
        Citizen.SetTimeout(1000, function()
          TriggerServerEvent('duty:onoff', 'on_duty')
        end)

        if exports['dopeNotify'] then
          exports['dopeNotify']:ShowNotification('info', '🔄 Chuyển job: ' .. oldJob.name .. ' → ' .. job.name, 4000)
        end

      -- Case 3: Từ on-duty job sang off-duty job cùng loại (police → offpolice)
      elseif wasOnDuty and IsOffDutyJob(job.name) then
        TriggerServerEvent('duty:jobLost', identifier, playerName, oldJob.name, oldJob.grade, 'switched_to_off_duty')

        if exports['dopeNotify'] then
          exports['dopeNotify']:ShowNotification('info', '🔴 Đã chuyển sang off duty: ' .. job.name, 3000)
        end
      end
    end
  end

  -- Reset trạng thái duty khi thay đổi job
  isOnDuty = IsOnDutyJob(job.name)
end)

---- markers
AddEventHandler('esx_duty:hasEnteredMarker', function(zone)
  if zone ~= nil then
      CurrentAction     = 'onoff'
      CurrentActionMsg  = _U('duty') .. (isOnDuty and ' (Tắt Duty)' or ' (Bật Duty)')
      -- Chỉ hiển thị thông báo dopeNotify
      if exports['dopeNotify'] then
        exports['dopeNotify']:ShowNotification('info', 'Nhấn [E] để ' .. (isOnDuty and 'TẮT' or 'BẬT') .. ' Duty', 3000)
      end
  end
end)

AddEventHandler('esx_duty:hasExitedMarker', function(zone)
  CurrentAction = nil
  -- Bỏ thông báo khi rời khỏi marker
end)

-- keycontrols
Citizen.CreateThread(function()
  while true do
      Citizen.Wait(1)

      local playerPed = GetPlayerPed(-1)
      
      local jobs = {
          'offambulance',
          'offpolice',
          'offmechanic',
          'offpolice2',
          'offtiembanh',
          'police',
          'ambulance',
          'mechanic',
          'police2',
          'tiembanh'
      }

      if CurrentAction ~= nil and not isReconnectMenuOpen then
          for k, v in pairs(jobs) do
              if PlayerData.job and PlayerData.job.name == v then
                  if IsControlJustPressed(0, Keys['E']) then
                      -- Chuyển đổi trạng thái duty
                      isOnDuty = not isOnDuty
                      local status = isOnDuty and 'on_duty' or 'off_duty'
                      TriggerServerEvent('duty:onoff', status)
                  end
              end
          end
      end
  end       
end)

-- Display markers
Citizen.CreateThread(function()
  while true do
      Wait(0)

      if PlayerData.job then
          local coords = GetEntityCoords(GetPlayerPed(-1))

          for k, v in pairs(Config.Zones) do
              -- Kiểm tra xem marker có phù hợp với job hiện tại không
              if ShouldShowMarker(k, PlayerData.job.name) then
                  if(v.Type ~= -1 and GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < Config.DrawDistance) then
                      DrawMarker(v.Type, v.Pos.x, v.Pos.y, v.Pos.z, 0.0, 0.0, 0.0, 0, 0.0, 0.0, v.Size.x, v.Size.y, v.Size.z, v.Color.r, v.Color.g, v.Color.b, 100, true, true, 2, nil, nil, false)
                  end
              end
          end
      else
          -- Debug: PlayerData.job chưa có
          Wait(1000) -- Giảm tần suất check khi chưa có job
      end
  end
end)

-- Enter / Exit marker events
Citizen.CreateThread(function()
  while true do
      Wait(0)

      if PlayerData.job then
          local coords      = GetEntityCoords(GetPlayerPed(-1))
          local isInMarker  = false
          local currentZone = nil

          for k, v in pairs(Config.Zones) do
              -- Chỉ kiểm tra marker phù hợp với job
              if ShouldShowMarker(k, PlayerData.job.name) then
                  if(GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < Config.InteractionDistance) then
                      isInMarker  = true
                      currentZone = k
                  end
              end
          end

          if (isInMarker and not HasAlreadyEnteredMarker) or (isInMarker and LastZone ~= currentZone) then
              HasAlreadyEnteredMarker = true
              LastZone                = currentZone
              TriggerEvent('esx_duty:hasEnteredMarker', currentZone)
          end

          if not isInMarker and HasAlreadyEnteredMarker then
              HasAlreadyEnteredMarker = false
              TriggerEvent('esx_duty:hasExitedMarker', LastZone)
          end
      end
  end
end)

-- Nhận thông báo từ server khi duty thay đổi
RegisterNetEvent('duty:statusChanged')
AddEventHandler('duty:statusChanged', function(status, message)
  isOnDuty = (status == 'on_duty')
  
  -- Chỉ giữ lại thông báo trạng thái với kiểm tra thời gian để tránh spam
  if not lastNotificationTime or (GetGameTimer() - lastNotificationTime) > 2000 then
    local statusText = isOnDuty and 'BẬT' or 'TẮT'
    local notificationMessage = message or ('Bạn đã ' .. statusText .. ' Duty thành công!')
    
    if exports['dopeNotify'] then
      exports['dopeNotify']:ShowNotification('success', notificationMessage, 4000)
    else
      -- Fallback notification
      SetNotificationTextEntry("STRING")
      AddTextComponentString(notificationMessage)
      DrawNotification(0, 1)
    end
    
    lastNotificationTime = GetGameTimer()
  end
  
  -- Cập nhật lại thông báo tại marker
  if CurrentAction == 'onoff' then
      CurrentActionMsg = _U('duty') .. (isOnDuty and ' (Tắt Duty)' or ' (Bật Duty)')
  end
end)

-- Nhận event hiển thị menu reconnect
RegisterNetEvent('duty:showReconnectMenu')
AddEventHandler('duty:showReconnectMenu', function(data)
  if isReconnectMenuOpen then return end
  
  isReconnectMenuOpen = true
  
  local message = data.message
  local session = data.session
  local recommendations = data.recommendations
  
  -- Tạo menu options
  local menuOptions = {
      {
          label = "🔄 Tiếp tục session cũ",
          description = "Tiếp tục làm việc từ session trước",
          action = "continue"
      },
      {
          label = "⏹️ Kết thúc session cũ",
          description = "Kết thúc session và về trạng thái off duty",
          action = "end_session"
      },
      {
          label = "🔄⏹️ Kết thúc và bắt đầu mới",
          description = "Kết thúc session cũ và bắt đầu session mới",
          action = "end_and_new"
      },
      {
          label = "❌ Bỏ qua",
          description = "Bỏ qua session cũ (không khuyến khích)",
          action = "ignore"
      }
  }
  
  -- Hiển thị menu
  ShowReconnectNativeMenu(message, menuOptions)
end)

-- Thêm hàm kiểm tra xem marker có phù hợp với job hiện tại không
function ShouldShowMarker(markerName, jobName)
    -- Ánh xạ từ tên marker đến các job được phép thấy marker đó
    local markerJobMap = {
        PoliceDuty = {'police', 'offpolice'},
        AmbulanceDuty = {'ambulance', 'offambulance'},
        MechanicDuty = {'mechanic', 'offmechanic'},
        Police2Duty = {'police2', 'offpolice2'},
        FoodDuty = {'tiembanh', 'offtiembanh'}
    }
    
    -- Kiểm tra xem job hiện tại có trong danh sách job được phép thấy marker không
    local allowedJobs = markerJobMap[markerName]
    if allowedJobs then
        for _, allowedJob in pairs(allowedJobs) do
            if jobName == allowedJob then
                return true
            end
        end
    end
    
    return false
end

-- Hàm kiểm tra xem có phải job off-duty không
function IsOffDutyJob(jobName)
    local offDutyJobs = {
        'offpolice', 'offpolice2', 'offambulance',
        'offmechanic', 'offtiembanh', 'offarmy'
    }

    for _, offJob in pairs(offDutyJobs) do
        if jobName == offJob then
            return true
        end
    end
    return false
end

-- Hàm kiểm tra xem có phải job on-duty không
function IsOnDutyJob(jobName)
    local onDutyJobs = {
        'police', 'police2', 'ambulance',
        'mechanic', 'tiembanh', 'army'
    }

    for _, onJob in pairs(onDutyJobs) do
        if jobName == onJob then
            return true
        end
    end
    return false
end

-- Hàm kiểm tra xem có phải bất kỳ duty job nào không (on hoặc off)
function IsAnyDutyJob(jobName)
    return IsOnDutyJob(jobName) or IsOffDutyJob(jobName)
end

-- Hàm lấy player identifier
function GetPlayerIdentifier()
    local playerData = ESX.GetPlayerData()
    if playerData and playerData.identifier then
        return playerData.identifier
    end
    return nil
end

-- Hàm hiển thị menu on duty khi join game
function ShowOnDutyMenu()
    if isReconnectMenuOpen then return end

    isReconnectMenuOpen = true

    local jobName = PlayerData.job.name
    local jobDisplayName = GetJobDisplayName(jobName)

    ShowSimpleOnDutyMenu(jobDisplayName)
end

-- Hàm lấy tên hiển thị của job
function GetJobDisplayName(jobName)
    local jobNames = {
        ['offpolice'] = 'CẢNH SÁT',
        ['offpolice2'] = 'CẢNH SÁT ĐẶC BIỆT',
        ['offambulance'] = 'Y TẾ',
        ['offmechanic'] = 'THỢ MÁY',
        ['offtiembanh'] = 'TIỆM BÁNH',
        ['offarmy'] = 'QUÂN ĐỘI'
    }

    return jobNames[jobName] or jobName:upper()
end

-- Hàm hiển thị menu on duty bắt buộc (không cho hủy)
function ShowSimpleOnDutyMenu(jobDisplayName)
    Citizen.CreateThread(function()
        local menuActive = true

        Citizen.CreateThread(function()
            while menuActive do
                Citizen.Wait(0)

                -- Disable ALL controls - không cho làm gì khác
                DisableAllControlActions(0)
                EnableControlAction(0, 176, true) -- Chỉ cho phép Enter

                -- Simple background
                DrawRect(0.5, 0.5, 0.35, 0.25, 0, 0, 0, 200)
                DrawRect(0.5, 0.5, 0.33, 0.23, 30, 30, 30, 220)

                -- Title
                SetTextFont(4)
                SetTextProportional(1)
                SetTextScale(0.6, 0.6)
                SetTextColour(255, 255, 255, 255)
                SetTextEntry("STRING")
                AddTextComponentString("BẮT BUỘC ON DUTY")
                SetTextCentre(true)
                DrawText(0.5, 0.40)

                -- Job info
                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.4, 0.4)
                SetTextColour(200, 200, 200, 255)
                SetTextEntry("STRING")
                AddTextComponentString(jobDisplayName)
                SetTextCentre(true)
                DrawText(0.5, 0.46)

                -- On duty button (pulsing effect)
                local pulse = math.sin(GetGameTimer() * 0.005) * 20 + 200
                local buttonColor = {34, 139, 34, pulse}
                DrawRect(0.5, 0.53, 0.25, 0.06, buttonColor[1], buttonColor[2], buttonColor[3], buttonColor[4])

                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.45, 0.45)
                SetTextColour(255, 255, 255, 255)
                SetTextEntry("STRING")
                AddTextComponentString("🟢 BẬT DUTY")
                SetTextCentre(true)
                DrawText(0.5, 0.515)

                -- Instructions (chỉ có ENTER)
                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.3, 0.3)
                SetTextColour(255, 215, 0, 255)
                SetTextEntry("STRING")
                AddTextComponentString("ENTER: Bắt đầu làm việc (BẮT BUỘC)")
                SetTextCentre(true)
                DrawText(0.5, 0.60)

                -- Handle input - CHỈ CHO PHÉP ENTER
                if IsControlJustPressed(0, 176) then -- Enter
                    PlaySoundFrontend(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)

                    -- Bật duty
                    TriggerServerEvent('duty:onoff', 'on_duty')

                    if exports['dopeNotify'] then
                        exports['dopeNotify']:ShowNotification('success', '🟢 Đã bật duty thành công! Chào mừng trở lại làm việc!', 5000)
                    end

                    menuActive = false
                    isReconnectMenuOpen = false
                    break
                end

                -- KHÔNG CHO PHÉP HỦY - Bỏ backspace và auto-close
            end
        end)
    end)
end

-- Hàm hiển thị menu on duty native với giao diện đẹp (deprecated)
function ShowOnDutyNativeMenu(message, options)
    Citizen.CreateThread(function()
        local menuActive = true
        local selectedIndex = 1
        local maxIndex = #options
        local animationProgress = 0.0

        Citizen.CreateThread(function()
            while menuActive do
                Citizen.Wait(0)

                -- Animation progress
                if animationProgress < 1.0 then
                    animationProgress = animationProgress + 0.05
                end

                -- Disable all controls
                DisableAllControlActions(0)
                EnableControlAction(0, 1, true) -- LookLeftRight
                EnableControlAction(0, 2, true) -- LookUpDown
                EnableControlAction(0, 172, true) -- Up Arrow
                EnableControlAction(0, 173, true) -- Down Arrow
                EnableControlAction(0, 176, true) -- Enter
                EnableControlAction(0, 177, true) -- Backspace

                -- Draw animated background with gradient effect
                local bgAlpha = math.floor(240 * animationProgress)
                DrawRect(0.5, 0.5, 0.75, 0.65, 0, 0, 0, bgAlpha)

                -- Draw main container with gradient
                DrawRect(0.5, 0.5, 0.72, 0.62, 15, 15, 25, math.floor(220 * animationProgress))
                DrawRect(0.5, 0.5, 0.70, 0.60, 25, 25, 35, math.floor(200 * animationProgress))

                -- Draw header gradient
                DrawRect(0.5, 0.22, 0.70, 0.08, 30, 144, 255, math.floor(180 * animationProgress))
                DrawRect(0.5, 0.22, 0.70, 0.06, 65, 105, 225, math.floor(160 * animationProgress))

                -- Draw title with glow effect
                SetTextFont(4)
                SetTextProportional(1)
                SetTextScale(0.9, 0.9)
                SetTextColour(255, 255, 255, math.floor(255 * animationProgress))
                SetTextEntry("STRING")
                AddTextComponentString("🎮 DUTY SYSTEM")
                SetTextCentre(true)
                DrawText(0.5, 0.20)
                SetTextCentre(false)

                -- Draw subtitle
                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.35, 0.35)
                SetTextColour(200, 200, 255, math.floor(200 * animationProgress))
                SetTextEntry("STRING")
                AddTextComponentString("Hệ thống quản lý duty tự động")
                SetTextCentre(true)
                DrawText(0.5, 0.26)
                SetTextCentre(false)

                -- Draw message with better formatting
                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.42, 0.42)
                SetTextColour(220, 220, 220, math.floor(255 * animationProgress))
                SetTextEntry("STRING")
                AddTextComponentString(message)
                SetTextCentre(true)
                DrawText(0.5, 0.38)
                SetTextCentre(false)

                -- Draw options with modern design
                for i, option in ipairs(options) do
                    local yPos = 0.52 + (i - 1) * 0.09
                    local optionAlpha = math.floor(255 * animationProgress)
                    local isSelected = (i == selectedIndex)

                    -- Option background with hover effect
                    if isSelected then
                        -- Animated selection background
                        local pulseAlpha = math.floor((math.sin(GetGameTimer() * 0.01) * 20 + 180) * animationProgress)

                        if option.action == "go_on_duty" then
                            -- Green gradient for on duty
                            DrawRect(0.5, yPos, 0.62, 0.07, 34, 139, 34, pulseAlpha)
                            DrawRect(0.5, yPos, 0.60, 0.065, 50, 205, 50, math.floor(200 * animationProgress))
                        else
                            -- Blue gradient for skip
                            DrawRect(0.5, yPos, 0.62, 0.07, 70, 130, 180, pulseAlpha)
                            DrawRect(0.5, yPos, 0.60, 0.065, 100, 149, 237, math.floor(200 * animationProgress))
                        end

                        -- Selection border
                        DrawRect(0.5, yPos - 0.035, 0.62, 0.002, 255, 255, 255, optionAlpha)
                        DrawRect(0.5, yPos + 0.035, 0.62, 0.002, 255, 255, 255, optionAlpha)
                        DrawRect(0.19, yPos, 0.002, 0.07, 255, 255, 255, optionAlpha)
                        DrawRect(0.81, yPos, 0.002, 0.07, 255, 255, 255, optionAlpha)
                    else
                        -- Normal background
                        DrawRect(0.5, yPos, 0.60, 0.065, 40, 40, 50, math.floor(120 * animationProgress))
                    end

                    -- Option icon and text
                    local textColor = isSelected and {255, 255, 255, optionAlpha} or {200, 200, 200, optionAlpha}

                    -- Draw option label
                    SetTextFont(0)
                    SetTextProportional(1)
                    SetTextScale(0.48, 0.48)
                    SetTextColour(textColor[1], textColor[2], textColor[3], textColor[4])
                    SetTextEntry("STRING")
                    AddTextComponentString(option.label)
                    SetTextCentre(true)
                    DrawText(0.5, yPos - 0.018)
                    SetTextCentre(false)

                    -- Draw option description
                    SetTextFont(0)
                    SetTextProportional(1)
                    SetTextScale(0.32, 0.32)
                    SetTextColour(textColor[1], textColor[2], textColor[3], math.floor(textColor[4] * 0.8))
                    SetTextEntry("STRING")
                    AddTextComponentString(option.description)
                    SetTextCentre(true)
                    DrawText(0.5, yPos + 0.008)
                    SetTextCentre(false)
                end

                -- Draw instructions with modern style
                local instructionY = 0.72
                DrawRect(0.5, instructionY, 0.70, 0.05, 20, 20, 30, math.floor(180 * animationProgress))

                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.32, 0.32)
                SetTextColour(255, 215, 0, math.floor(255 * animationProgress))
                SetTextEntry("STRING")
                AddTextComponentString("⬆️⬇️ Di chuyển   |   ENTER Chọn   |   BACKSPACE Bỏ qua")
                SetTextCentre(true)
                DrawText(0.5, instructionY - 0.012)
                SetTextCentre(false)

                -- Handle input with better feedback
                if IsControlJustPressed(0, 172) then -- Up Arrow
                    selectedIndex = selectedIndex - 1
                    if selectedIndex < 1 then
                        selectedIndex = maxIndex
                    end
                    PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                end

                if IsControlJustPressed(0, 173) then -- Down Arrow
                    selectedIndex = selectedIndex + 1
                    if selectedIndex > maxIndex then
                        selectedIndex = 1
                    end
                    PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                end

                if IsControlJustPressed(0, 176) then -- Enter
                    local selectedOption = options[selectedIndex]
                    PlaySoundFrontend(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)

                    -- Show loading effect
                    DrawRect(0.5, 0.5, 0.70, 0.60, 0, 0, 0, 200)
                    SetTextFont(0)
                    SetTextProportional(1)
                    SetTextScale(0.5, 0.5)
                    SetTextColour(255, 255, 255, 255)
                    SetTextEntry("STRING")
                    AddTextComponentString("⏳ Đang xử lý...")
                    SetTextCentre(true)
                    DrawText(0.5, 0.5)
                    SetTextCentre(false)

                    if selectedOption.action == "go_on_duty" then
                        -- Bật duty ngay lập tức
                        TriggerServerEvent('duty:onoff', 'on_duty')

                        if exports['dopeNotify'] then
                            exports['dopeNotify']:ShowNotification('success', '🟢 Đã bật duty thành công! Chào mừng bạn trở lại làm việc!', 5000)
                        end
                    else
                        -- Skip - không làm gì
                        if exports['dopeNotify'] then
                            exports['dopeNotify']:ShowNotification('info', 'ℹ️ Bạn có thể bật duty tại marker khi cần thiết', 4000)
                        end
                    end

                    menuActive = false
                    isReconnectMenuOpen = false
                    break
                end

                if IsControlJustPressed(0, 177) then -- Backspace
                    PlaySoundFrontend(-1, "BACK", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)

                    if exports['dopeNotify'] then
                        exports['dopeNotify']:ShowNotification('info', 'ℹ️ Menu đã đóng. Bạn có thể bật duty tại marker', 3000)
                    end

                    menuActive = false
                    isReconnectMenuOpen = false
                    break
                end
            end
        end)
    end)
end

-- Hàm hiển thị menu reconnect native
function ShowReconnectNativeMenu(message, options)
    Citizen.CreateThread(function()
        -- Disable controls while menu is open
        local menuActive = true
        local selectedIndex = 1
        local maxIndex = #options

        -- Display instructions
        Citizen.CreateThread(function()
            while menuActive do
                Citizen.Wait(0)

                -- Disable all controls
                DisableAllControlActions(0)
                EnableControlAction(0, 1, true) -- LookLeftRight
                EnableControlAction(0, 2, true) -- LookUpDown
                EnableControlAction(0, 172, true) -- Up Arrow
                EnableControlAction(0, 173, true) -- Down Arrow
                EnableControlAction(0, 176, true) -- Enter
                EnableControlAction(0, 177, true) -- Backspace

                -- Draw background
                DrawRect(0.5, 0.5, 0.65, 0.85, 0, 0, 0, 220)
                DrawRect(0.5, 0.5, 0.63, 0.83, 30, 30, 30, 200)

                -- Draw title
                SetTextFont(4)
                SetTextProportional(1)
                SetTextScale(0.9, 0.9)
                SetTextColour(255, 255, 255, 255)
                SetTextEntry("STRING")
                AddTextComponentString("🔄 DUTY RECONNECT SYSTEM")
                DrawText(0.18, 0.12)

                -- Draw message
                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.42, 0.42)
                SetTextColour(200, 200, 200, 255)
                SetTextEntry("STRING")
                AddTextComponentString(message)
                DrawText(0.18, 0.22)

                -- Draw options
                for i, option in ipairs(options) do
                    local yPos = 0.42 + (i - 1) * 0.09
                    local color = {180, 180, 180, 255}
                    local bgColor = {40, 40, 40, 150}

                    if i == selectedIndex then
                        color = {255, 255, 255, 255}
                        bgColor = {70, 130, 180, 200}
                        -- Highlight effect
                        DrawRect(0.5, yPos, 0.55, 0.07, bgColor[1], bgColor[2], bgColor[3], bgColor[4])
                    else
                        DrawRect(0.5, yPos, 0.55, 0.07, bgColor[1], bgColor[2], bgColor[3], bgColor[4])
                    end

                    -- Draw option text
                    SetTextFont(0)
                    SetTextProportional(1)
                    SetTextScale(0.45, 0.45)
                    SetTextColour(color[1], color[2], color[3], color[4])
                    SetTextEntry("STRING")
                    AddTextComponentString(option.label)
                    DrawText(0.23, yPos - 0.025)

                    -- Draw description
                    SetTextFont(0)
                    SetTextProportional(1)
                    SetTextScale(0.32, 0.32)
                    SetTextColour(color[1], color[2], color[3], 180)
                    SetTextEntry("STRING")
                    AddTextComponentString(option.description)
                    DrawText(0.23, yPos + 0.015)
                end

                -- Draw instructions
                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.35, 0.35)
                SetTextColour(255, 255, 0, 255)
                SetTextEntry("STRING")
                AddTextComponentString("⬆️⬇️ Di chuyển | ENTER Chọn | BACKSPACE Hủy")
                DrawText(0.18, 0.82)

                -- Handle input
                if IsControlJustPressed(0, 172) then -- Up Arrow
                    selectedIndex = selectedIndex - 1
                    if selectedIndex < 1 then
                        selectedIndex = maxIndex
                    end
                    PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                end

                if IsControlJustPressed(0, 173) then -- Down Arrow
                    selectedIndex = selectedIndex + 1
                    if selectedIndex > maxIndex then
                        selectedIndex = 1
                    end
                    PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                end

                if IsControlJustPressed(0, 176) then -- Enter
                    local selectedOption = options[selectedIndex]
                    PlaySoundFrontend(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)

                    -- Send choice to server
                    TriggerServerEvent('duty:handleReconnectChoice', selectedOption.action)

                    menuActive = false
                    isReconnectMenuOpen = false

                    -- Show confirmation
                    if exports['dopeNotify'] then
                        exports['dopeNotify']:ShowNotification('success', 'Đã chọn: ' .. selectedOption.label, 3000)
                    end
                    break
                end

                if IsControlJustPressed(0, 177) then -- Backspace
                    PlaySoundFrontend(-1, "BACK", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)

                    -- Default to ignore
                    TriggerServerEvent('duty:handleReconnectChoice', 'ignore')

                    menuActive = false
                    isReconnectMenuOpen = false

                    if exports['dopeNotify'] then
                        exports['dopeNotify']:ShowNotification('info', 'Đã hủy - bỏ qua session cũ', 3000)
                    end
                    break
                end
            end
        end)
    end)
end

-- Command để test menu reconnect (chỉ dành cho admin)
RegisterCommand('testdutymenu', function()
    if PlayerData.job and PlayerData.job.grade >= 5 then -- Chỉ admin
        local testData = {
            message = "🧪 TEST MENU\n\nBạn có session POLICE đang active từ 2 giờ trước.\n\nBạn muốn:",
            session = {
                job = "police",
                start_time = os.time() - 7200 -- 2 hours ago
            },
            recommendations = {}
        }

        TriggerEvent('duty:showReconnectMenu', testData)
    else
        if exports['dopeNotify'] then
            exports['dopeNotify']:ShowNotification('error', 'Bạn không có quyền sử dụng lệnh này!', 3000)
        end
    end
end, false)

-- Command để test menu on duty (chỉ dành cho admin)
RegisterCommand('testonduty', function()
    if PlayerData.job and PlayerData.job.grade >= 5 then -- Chỉ admin
        ShowSimpleOnDutyMenu("TEST JOB")
    else
        if exports['dopeNotify'] then
            exports['dopeNotify']:ShowNotification('error', 'Bạn không có quyền sử dụng lệnh này!', 3000)
        end
    end
end, false)

-- Export functions for other resources
exports('isOnDuty', function()
    return isOnDuty
end)

exports('isReconnectMenuOpen', function()
    return isReconnectMenuOpen
end)

exports('showReconnectMenu', function(data)
    TriggerEvent('duty:showReconnectMenu', data)
end)
