# FiveM Duty System với Node.js Dashboard

Hệ thống duty tracking hoàn chỉnh cho FiveM với web dashboard hiện đại, tích hợp database và xử lý disconnect/reconnect thông minh.

## ✨ Tính năng

### 🎮 FiveM Features
- **Duty On/Off System** - Bật/tắt duty tại các marker
- **Multi-Job Support** - Hỗ trợ nhiều job (Police, EMS, Mechanic, v.v.)
- **Smart Reconnect** - Menu lựa chọn khi player reconnect
- **Auto Disconnect Handling** - Tự động off duty khi player disconnect
- **Session Management** - <PERSON> dõi thời gian duty chính xác
- **Notification System** - Thông báo đẹp với dopeNotify

### 🌐 Web Dashboard Features
- **Modern UI** - Giao diện hiện đại với gradient và animations
- **Real-time Data** - Cập nhật dữ liệu real-time
- **Job Filtering** - Lọc theo job với job cards đẹp mắt
- **Date Range Selection** - Chọn ngà<PERSON>/khoảng thời gian
- **Player Statistics** - Thống kê thời gian online chi tiết
- **Active Sessions** - Xem ai đang online duty
- **Export to Excel** - Xuất dữ liệu ra CSV
- **Responsive Design** - Tương thích mobile

### 🗄️ Database Features
- **MySQL Integration** - Lưu trữ dữ liệu bền vững
- **Session Tracking** - Theo dõi từng phiên duty
- **Time Calculation** - Tính toán thời gian chính xác
- **Statistics** - Thống kê tổng hợp
- **Auto Cleanup** - Dọn dẹp sessions cũ tự động

## 🚀 Cài đặt

### 1. Node.js Server (đã có sẵn)

Server hiện tại đã được cập nhật với đầy đủ tính năng. Chỉ cần restart:

```bash
# Restart server để áp dụng các thay đổi mới
npm start
```

### 2. FiveM Resource

Copy các file sau vào thư mục `resources/esx_duty_advanced/`:

```
resources/esx_duty_advanced/
├── fxmanifest.lua
├── config.lua
├── server.lua
├── client.lua
└── locales/
    ├── en.lua
    └── vi.lua
```

Thêm vào `server.cfg`:
```cfg
ensure esx_duty_advanced
```

### 3. Cấu hình

Trong `config.lua`, đảm bảo URL đúng:
```lua
Config.NodeServer = {
    enabled = true,
    url = 'http://localhost:3000/api/fivem'
}
```

## 🎯 Sử dụng

### Cho Players
1. **Bật/Tắt Duty**: Đến marker và nhấn `E`
2. **Reconnect Menu**: Tự động hiện khi join lại server nếu có session cũ
3. **Xem thống kê**: Truy cập web dashboard

### Cho Admins
- `/dutystats` - Xem URL dashboard
- `/dutycleanup [hours]` - Dọn dẹp sessions cũ (mặc định 24h)
- `/testdutymenu` - Test reconnect menu

### Web Dashboard
Truy cập: `http://localhost:3000`

**4 Tab chính:**
- **Duty Logs**: Xem tất cả logs với bộ lọc
- **Thống kê Players**: Thời gian online tổng hợp
- **Phiên Duty**: Chi tiết từng session (on → off)
- **Đang Online**: Ai đang duty hiện tại

## 🔧 API Endpoints Mới

### FiveM Integration
- `POST /api/fivem/duty-log` - Ghi duty log
- `POST /api/fivem/player-disconnect` - Xử lý disconnect
- `POST /api/fivem/check-duty-status` - Kiểm tra status khi join
- `POST /api/fivem/player-reconnect` - Xử lý reconnect choice

### Data Retrieval
- `GET /api/fivem/duty-sessions` - Chi tiết sessions
- `GET /api/fivem/active-sessions` - Sessions đang active
- `GET /api/fivem/daily-stats` - Thống kê theo ngày

### Management
- `POST /api/fivem/cleanup-sessions` - Dọn dẹp sessions cũ

## 🎨 Tính năng nổi bật

### ⚡ Smart Reconnect System
Khi player thoát game mà chưa off duty:
1. Hệ thống tự động detect khi player join lại
2. Hiển thị menu lựa chọn với 4 options:
   - 🔄 **Tiếp tục session cũ**
   - ⏹️ **Kết thúc session cũ**
   - 🔄⏹️ **Kết thúc và bắt đầu mới**
   - ❌ **Bỏ qua**
3. Tính toán thời gian chính xác

### 🎨 Modern Web Dashboard
- **Glass morphism design** với backdrop blur
- **Gradient job cards** cho từng nghề nghiệp
- **Interactive elements** với smooth animations
- **Real-time updates** mỗi 30 giây
- **Export to Excel** với UTF-8 support

### 🔄 Auto Session Management
- Tự động kết thúc session khi disconnect
- Cleanup sessions cũ quá 24 giờ
- Theo dõi thời gian duty chính xác (11:00 → 12:00 = 1 giờ)
- Thống kê chi tiết: tổng giờ, số sessions, trung bình/session

## 📊 Database Schema

### duty_logs
Lưu trữ mọi hành động duty:
```sql
id, identifier, name, job, grade, status, timestamp
```

### duty_sessions  
Theo dõi từng phiên duty:
```sql
id, identifier, name, job, grade, start_time, end_time, duration_minutes, status
```

### duty_statistics
Thống kê tổng hợp:
```sql
id, identifier, name, job, total_sessions, total_minutes, total_hours, last_on_duty, last_off_duty
```

## 🎮 Job Configuration

Hỗ trợ các job sau (có thể mở rộng):

- 🛡️ **Police** - Cảnh sát
- 👮 **Police2** - Cảnh sát đặc biệt  
- 🚑 **Ambulance** - Y tế
- 🔧 **Mechanic** - Thợ máy
- 🎂 **Tiembanh** - Tiệm bánh
- ✈️ **Army** - Quân đội

Mỗi job có:
- Icon riêng
- Màu sắc riêng
- Marker riêng
- Thống kê riêng

## 🔧 Troubleshooting

### FiveM không gửi được data
1. Kiểm tra Node.js server đang chạy
2. Kiểm tra URL trong config.lua
3. Xem console logs để debug

### Menu reconnect không hiện
1. Đảm bảo player có session active
2. Kiểm tra ESX đã load
3. Test với `/testdutymenu`

### Web dashboard không load
1. Restart Node.js server
2. Kiểm tra port 3000
3. Clear browser cache

## 🎯 Test Commands

```lua
-- Test duty (admin only)
/testduty on_duty
/testduty off_duty

-- Test disconnect
/testdisconnect

-- Test reconnect menu
/testdutymenu

-- View stats
/dutystats

-- Cleanup old sessions
/dutycleanup 24
```

## 📱 Mobile Support

Dashboard hoàn toàn responsive:
- Job cards tự động resize
- Tables scroll horizontal
- Touch-friendly buttons
- Optimized cho mobile

## 🚀 Performance

- **Database indexing** cho queries nhanh
- **Connection pooling** cho MySQL
- **Efficient queries** với prepared statements
- **Auto cleanup** để tránh database bloat
- **Caching** cho static assets

## 📝 License

MIT License - Made with ❤️ for FiveM Community

---

**🎉 Hệ thống hoàn chỉnh với:**
- ✅ Giao diện web đẹp mắt
- ✅ Xử lý disconnect/reconnect thông minh  
- ✅ Tính toán thời gian chính xác
- ✅ Database integration hoàn chỉnh
- ✅ Multi-job support
- ✅ Mobile responsive
- ✅ Export Excel
- ✅ Real-time updates
