-- FiveM Duty System - Server Side
-- Tích hợp với Node.js Dashboard

-- Configuration
local Config = {
    nodeServerUrl = 'http://localhost:3000/api/fivem',
    enableLogging = true,
    dutyJobs = {
        'police', 'police2', 'ambulance', 'mechanic', 'tiembanh', 'army',
        'offpolice', 'offpolice2', 'offambulance', 'offmechanic', 'offtiembanh', 'offarmy'
    }
}

-- Hàm gửi duty log đến Node.js server
local function SendToNodeServer(identifier, name, job, grade, status, timestamp)
    local data = {
        identifier = identifier,
        name = name,
        job = job,
        grade = grade,
        status = status,
        timestamp = timestamp or os.date("!%Y-%m-%dT%H:%M:%SZ")
    }

    PerformHttpRequest(Config.nodeServerUrl .. '/duty-log', function(errorCode, resultData, resultHeaders)
        if errorCode == 200 or errorCode == 201 then
            if Config.enableLogging then
                local response = json.decode(resultData)
                print(('[Duty System] ✅ %s (%s) - %s duty logged successfully'):format(name, job, status))
            end
        else
            if Config.enableLogging then
                print(('[Duty System] ❌ Error logging duty: %s - %s'):format(errorCode, resultData))
            end
        end
    end, 'POST', json.encode(data), {
        ['Content-Type'] = 'application/json'
    })
end

-- Hàm kiểm tra duty status khi player join
local function CheckDutyStatusOnJoin(identifier, name)
    local data = {
        identifier = identifier,
        name = name
    }

    PerformHttpRequest(Config.nodeServerUrl .. '/check-duty-status', function(errorCode, resultData, resultHeaders)
        if errorCode == 200 or errorCode == 201 then
            local response = json.decode(resultData)
            if response.success and response.data.hasActiveSessions then
                -- Player có session active, hiển thị menu lựa chọn
                local source = GetPlayerFromIdentifier(identifier)
                if source then
                    TriggerClientEvent('duty:showReconnectMenu', source, response.data)
                end
            end
        else
            if Config.enableLogging then
                print(('[Duty System] ❌ Error checking duty status: %s'):format(errorCode))
            end
        end
    end, 'POST', json.encode(data), {
        ['Content-Type'] = 'application/json'
    })
end

-- Hàm xử lý player disconnect
local function SendPlayerDisconnect(identifier, name, reason)
    local data = {
        identifier = identifier,
        name = name,
        reason = reason or 'disconnect'
    }

    PerformHttpRequest(Config.nodeServerUrl .. '/player-disconnect', function(errorCode, resultData, resultHeaders)
        if errorCode == 200 or errorCode == 201 then
            if Config.enableLogging then
                local response = json.decode(resultData)
                print(('[Duty System] 🔄 %s disconnected - %s'):format(name, response.data.message))
            end
        else
            if Config.enableLogging then
                print(('[Duty System] ❌ Error handling disconnect: %s'):format(errorCode))
            end
        end
    end, 'POST', json.encode(data), {
        ['Content-Type'] = 'application/json'
    })
end

-- Hàm xử lý reconnect choice
local function HandleReconnectChoice(identifier, name, action)
    local data = {
        identifier = identifier,
        name = name,
        action = action
    }

    PerformHttpRequest(Config.nodeServerUrl .. '/player-reconnect', function(errorCode, resultData, resultHeaders)
        if errorCode == 200 or errorCode == 201 then
            local response = json.decode(resultData)
            if Config.enableLogging then
                print(('[Duty System] 🔄 %s reconnect handled: %s'):format(name, response.data.message))
            end
            
            -- Gửi thông báo cho client
            local source = GetPlayerFromIdentifier(identifier)
            if source then
                local actionMessages = {
                    continue = 'Đã tiếp tục session duty cũ',
                    end_session = 'Đã kết thúc session cũ',
                    end_and_new = 'Đã kết thúc session cũ và bắt đầu mới',
                    ignore = 'Đã bỏ qua session cũ'
                }
                
                TriggerClientEvent('duty:statusChanged', source, 'info', actionMessages[action])
            end
        else
            if Config.enableLogging then
                print(('[Duty System] ❌ Error handling reconnect: %s'):format(errorCode))
            end
        end
    end, 'POST', json.encode(data), {
        ['Content-Type'] = 'application/json'
    })
end

-- Hàm helper để lấy player từ identifier
function GetPlayerFromIdentifier(identifier)
    local xPlayers = ESX.GetPlayers()
    for i=1, #xPlayers, 1 do
        local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if xPlayer.identifier == identifier then
            return xPlayers[i]
        end
    end
    return nil
end

-- Event xử lý duty on/off
RegisterServerEvent('duty:onoff')
AddEventHandler('duty:onoff', function(status)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    
    if xPlayer then
        local identifier = xPlayer.identifier
        local name = GetPlayerName(_source)
        local job = xPlayer.job.name
        local grade = xPlayer.job.grade
        
        -- Kiểm tra xem job có trong danh sách duty jobs không
        local isDutyJob = false
        for _, dutyJob in pairs(Config.dutyJobs) do
            if job == dutyJob then
                isDutyJob = true
                break
            end
        end
        
        if isDutyJob then
            -- Gửi đến Node.js server
            SendToNodeServer(identifier, name, job, grade, status)
            
            -- Thông báo cho client
            local statusMessage = status == 'on_duty' and 'BẬT' or 'TẮT'
            TriggerClientEvent('duty:statusChanged', _source, status, 'Bạn đã ' .. statusMessage .. ' Duty thành công!')
            
            if Config.enableLogging then
                print(('[Duty System] 📋 %s (%s) - %s duty'):format(name, job, status))
            end
        else
            TriggerClientEvent('duty:statusChanged', _source, 'error', 'Job của bạn không hỗ trợ duty system!')
        end
    end
end)

-- Event kiểm tra duty status khi join
RegisterServerEvent('duty:checkStatusOnJoin')
AddEventHandler('duty:checkStatusOnJoin', function()
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    
    if xPlayer then
        local identifier = xPlayer.identifier
        local name = GetPlayerName(_source)
        
        if Config.enableLogging then
            print(('[Duty System] 🔍 %s joined - checking duty status...'):format(name))
        end
        
        -- Delay nhỏ để đảm bảo client đã sẵn sàng
        Citizen.SetTimeout(2000, function()
            CheckDutyStatusOnJoin(identifier, name)
        end)
    end
end)

-- Event xử lý reconnect choice
RegisterServerEvent('duty:handleReconnectChoice')
AddEventHandler('duty:handleReconnectChoice', function(action)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    
    if xPlayer then
        local identifier = xPlayer.identifier
        local name = GetPlayerName(_source)
        
        HandleReconnectChoice(identifier, name, action)
        
        if Config.enableLogging then
            print(('[Duty System] 🎯 %s chose reconnect action: %s'):format(name, action))
        end
    end
end)

-- Xử lý khi player disconnect
AddEventHandler('playerDropped', function(reason)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    
    if xPlayer then
        local identifier = xPlayer.identifier
        local name = GetPlayerName(_source)
        local job = xPlayer.job.name
        
        -- Chỉ xử lý cho các job có duty system
        local isDutyJob = false
        for _, dutyJob in pairs(Config.dutyJobs) do
            if job == dutyJob then
                isDutyJob = true
                break
            end
        end
        
        if isDutyJob then
            if Config.enableLogging then
                print(('[Duty System] 🚪 %s (%s) disconnected: %s'):format(name, job, reason))
            end
            
            -- Gửi thông tin disconnect để tự động off duty
            SendPlayerDisconnect(identifier, name, reason)
        end
    end
end)

-- Commands cho admin
RegisterCommand('dutystats', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer and xPlayer.getGroup() == 'admin' then
        TriggerClientEvent('chat:addMessage', source, {
            color = { 0, 255, 0 },
            multiline = true,
            args = { "Duty System", "Check web dashboard at: http://localhost:3000" }
        })
    end
end, false)

RegisterCommand('dutycleanup', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer and xPlayer.getGroup() == 'admin' then
        local maxHours = tonumber(args[1]) or 24
        
        PerformHttpRequest(Config.nodeServerUrl .. '/cleanup-sessions', function(errorCode, resultData, resultHeaders)
            if errorCode == 200 or errorCode == 201 then
                local response = json.decode(resultData)
                TriggerClientEvent('chat:addMessage', source, {
                    color = { 0, 255, 0 },
                    multiline = true,
                    args = { "Duty System", response.data.message or "Cleanup completed!" }
                })
            else
                TriggerClientEvent('chat:addMessage', source, {
                    color = { 255, 0, 0 },
                    multiline = true,
                    args = { "Duty System", "Cleanup failed: " .. errorCode }
                })
            end
        end, 'POST', json.encode({ maxHours = maxHours }), {
            ['Content-Type'] = 'application/json'
        })
    end
end, false)

-- Khởi tạo
Citizen.CreateThread(function()
    if Config.enableLogging then
        print('[Duty System] 🚀 FiveM-NodeJS Integration loaded successfully!')
        print('[Duty System] 📊 Node.js Server URL: ' .. Config.nodeServerUrl)
        print('[Duty System] 🔧 Features enabled:')
        print('  - Duty logging to database')
        print('  - Player disconnect handling')
        print('  - Reconnect session management')
        print('  - Web dashboard integration')
        print('[Duty System] 💻 Admin commands:')
        print('  - /dutystats - View dashboard URL')
        print('  - /dutycleanup [hours] - Cleanup old sessions')
    end
end)
