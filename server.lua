-- FiveM Duty System Integration với Node.js Server
-- Thêm code này vào server-side script của bạn

-- <PERSON><PERSON><PERSON> bảo ESX đã được khởi tạo
ESX = exports["es_extended"]:getSharedObject()

-- <PERSON><PERSON><PERSON> hình <PERSON>.js server
local CONFIG = {
    nodeServerUrl = 'https://med.kaitomc.site/api/fivem', -- URL của Node.js server (PRODUCTION)
    timeout = 5000, -- Timeout cho HTTP request (ms)
    retryAttempts = 3, -- <PERSON><PERSON> lần thử lại khi gửi thất bại
    enableLogging = true -- Bật/tắt logging
}

-- C<PERSON><PERSON> hình Discord Webhooks cho từng job (giữ nguyên từ code cũ)
local webhooks = {
    police = 'https://discord.com/api/webhooks/1388531558851547147/lRWL7P4XfJXguK4bwFzAOQTm5qOcJIOfx6NK56GWeKwwUJQDUcLwfd9iwB5X-K2TnTDd',
    ambulance = 'https://discord.com/api/webhooks/1388531712392302683/q4QTqrQCCKbyhsienL7W1sKt4gEGjPGkEELjv038TYxP1Ba1WkaHGwqpyfLoyFPF00vB',
    mechanic = 'https://discord.com/api/webhooks/1388530359288991856/g4D_lsRNmk8mtZAnCzMmIxmcN5nSTYIEWbZF12w_lgJt0zbeGFBDkZp-x57zz3ycFEV_',
    army = 'YOUR_ARMY_WEBHOOK_URL_HERE'
}

-- Hàm gửi dữ liệu đến Node.js server
local function SendToNodeServer(identifier, name, job, grade, status, attempt)
    attempt = attempt or 1
    
    if attempt > CONFIG.retryAttempts then
        if CONFIG.enableLogging then
            print(('[Duty Integration] Đã thử %d lần, bỏ qua gửi đến Node.js server'):format(CONFIG.retryAttempts))
        end
        return
    end

    local data = {
        identifier = identifier,
        name = name,
        job = job,
        grade = grade,
        status = status,
        timestamp = os.date('!%Y-%m-%dT%H:%M:%SZ') -- ISO 8601 format
    }

    PerformHttpRequest(CONFIG.nodeServerUrl .. '/duty-log', function(errorCode, resultData, resultHeaders)
        if errorCode == 200 or errorCode == 201 then
            if CONFIG.enableLogging then
                print(('[Duty Integration] Gửi dữ liệu đến Node.js server thành công - %s (%s) - %s'):format(name, identifier, status))
            end
        else
            if CONFIG.enableLogging then
                print(('[Duty Integration] Lỗi khi gửi đến Node.js server (Lần thử %d/%d): %s - %s'):format(attempt, CONFIG.retryAttempts, errorCode, resultData))
            end
            
            -- Thử lại sau 2 giây
            Citizen.SetTimeout(2000, function()
                SendToNodeServer(identifier, name, job, grade, status, attempt + 1)
            end)
        end
    end, 'POST', json.encode(data), {
        ['Content-Type'] = 'application/json'
    })
end

-- Hàm gửi log lên Discord (giữ nguyên từ code cũ)
local function SendDiscordLog(identifier, name, job, grade, status)
    local webhook = webhooks[job]
    if not webhook then
        if CONFIG.enableLogging then
            print(('[Duty Log] Lỗi: Không tìm thấy webhook cho job %s'):format(job))
        end
        return
    end

    local timestamp = os.date('%Y-%m-%d %H:%M:%S')
    local color = (status == 'on_duty') and 0x00FF00 or 0xFF0000
    local embed = {
        {
            ["title"] = "Duty Log - " .. job:upper(),
            ["color"] = color,
            ["fields"] = {
                { ["name"] = "Người chơi", ["value"] = name, ["inline"] = true },
                { ["name"] = "Identifier", ["value"] = identifier, ["inline"] = true },
                { ["name"] = "Công việc", ["value"] = job, ["inline"] = true },
                { ["name"] = "Cấp bậc", ["value"] = tostring(grade), ["inline"] = true },
                { ["name"] = "Trạng thái", ["value"] = (status == 'on_duty') and 'Bật Duty' or 'Tắt Duty', ["inline"] = true },
                { ["name"] = "Thời gian", ["value"] = timestamp, ["inline"] = true }
            },
            ["footer"] = { ["text"] = "ESX Duty Log System" },
            ["timestamp"] = os.date('!%Y-%m-%dT%H:%M:%SZ')
        }
    }

    PerformHttpRequest(webhook, function(err, text, headers) 
        if err ~= 200 then
            if CONFIG.enableLogging then
                print(('[Duty Log] Lỗi khi gửi log lên Discord: %s - %s'):format(err, text))
            end
        else
            if CONFIG.enableLogging then
                print(('[Duty Log] Gửi log lên Discord thành công - Job: %s'):format(job))
            end
        end
    end, 'POST', json.encode({
        username = job:upper() .. " Duty Logs",
        embeds = embed
    }), { ['Content-Type'] = 'application/json' })
end

-- Hàm ghi log vào database (giữ nguyên từ code cũ)
local function LogDutyStatus(identifier, name, job, grade, status)
    if not MySQL then
        if CONFIG.enableLogging then
            print('[Duty Log] Lỗi: MySQL không được khởi tạo!')
        end
        return
    end

    MySQL.Async.execute(
        'INSERT INTO duty_logs (identifier, name, job, grade, status, timestamp) VALUES (@identifier, @name, @job, @grade, @status, NOW())',
        {
            ['@identifier'] = identifier,
            ['@name'] = name,
            ['@job'] = job,
            ['@grade'] = grade,
            ['@status'] = status
        },
        function(rowsAffected)
            if rowsAffected > 0 then
                if CONFIG.enableLogging then
                    print(('[Duty Log] Ghi log vào database thành công: %s (%s) - Job: %s, Grade: %s, Status: %s'):format(name, identifier, job, grade, status))
                end
            else
                if CONFIG.enableLogging then
                    print('[Duty Log] Lỗi khi ghi log vào database!')
                end
            end
        end
    )
end

-- Xử lý sự kiện on/off duty (cập nhật từ code cũ)
RegisterServerEvent('duty:onoff')
AddEventHandler('duty:onoff', function(status)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if not xPlayer then
        if CONFIG.enableLogging then
            print(('[Duty Log] Lỗi: Không thể lấy xPlayer cho source %s'):format(_source))
        end
        return
    end

    local job = xPlayer.job.name
    local grade = xPlayer.job.grade
    local identifier = xPlayer.identifier
    local name = GetPlayerName(_source)

    local allowedJobs = { 'offambulance', 'offpolice', 'offmechanic', 'offpolice2', 'offtiembanh', 'police','police2','tiembanh', 'ambulance', 'mechanic' }
    if not table.contains(allowedJobs, job) then
        TriggerClientEvent('esx:showNotification', _source, 'Công việc của bạn không hỗ trợ bật/tắt duty!')
        return
    end

    local finalJob = job
    local finalStatus = status

    if status == 'off_duty' then
        if job == 'police' then
            xPlayer.setJob('offpolice', grade)
            finalJob = 'police'
        elseif job == 'police2' then
            xPlayer.setJob('offpolice2', grade)
            finalJob = 'police2'
        elseif job == 'tiembanh' then
            xPlayer.setJob('offtiembanh', grade)
            finalJob = 'tiembanh'
        elseif job == 'ambulance' then
            xPlayer.setJob('offambulance', grade)
            finalJob = 'ambulance'
        elseif job == 'mechanic' then
            xPlayer.setJob('offmechanic', grade)
            finalJob = 'mechanic'
        end
        TriggerClientEvent('duty:statusChanged', _source, 'off_duty')
    elseif status == 'on_duty' then
        if job == 'offpolice' then
            xPlayer.setJob('police', grade)
            finalJob = 'police'
        elseif job == 'offpolice2' then
            xPlayer.setJob('police2', grade)
            finalJob = 'police2'
        elseif job == 'offtiembanh' then
            xPlayer.setJob('tiembanh', grade)
            finalJob = 'tiembanh'
        elseif job == 'offambulance' then
            xPlayer.setJob('ambulance', grade)
            finalJob = 'ambulance'
        elseif job == 'offmechanic' then
            xPlayer.setJob('mechanic', grade)
            finalJob = 'mechanic'
        end
        TriggerClientEvent('duty:statusChanged', _source, 'on_duty')
    end

    -- Gửi dữ liệu đến tất cả các hệ thống
    LogDutyStatus(identifier, name, finalJob, grade, finalStatus)
    SendDiscordLog(identifier, name, finalJob, grade, finalStatus)
    SendToNodeServer(identifier, name, finalJob, grade, finalStatus) -- Thêm dòng này
end)

-- Hàm kiểm tra table.contains (giữ nguyên từ code cũ)
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

-- Cập nhật thông báo khi duty thay đổi (giữ nguyên từ code cũ)
RegisterNetEvent('duty:statusChanged')
AddEventHandler('duty:statusChanged', function(status)
    isOnDuty = (status == 'on_duty')
    exports['dopeNotify']:ShowNotification('info', 'Bạn đã ' .. (isOnDuty and 'bật duty' or 'tắt duty') .. ' thành công!', 3000)
    
    if CurrentAction == 'onoff' then
        CurrentActionMsg = _U('duty') .. (isOnDuty and ' (Tắt Duty)' or ' (Bật Duty)')
    end
end)

-- Hàm gửi thông tin disconnect để tự động off duty trên web
local function SendPlayerDisconnect(identifier, name, reason)
    local data = {
        identifier = identifier,
        name = name,
        reason = reason or 'disconnect'
    }

    PerformHttpRequest(CONFIG.nodeServerUrl .. '/player-disconnect', function(errorCode, resultData, resultHeaders)
        if errorCode == 200 or errorCode == 201 then
            if CONFIG.enableLogging then
                print(('[Duty Integration] 🔄 %s disconnected - web auto off duty processed'):format(name))
            end
        else
            if CONFIG.enableLogging then
                print(('[Duty Integration] ❌ Error handling disconnect: %s'):format(errorCode))
            end
        end
    end, 'POST', json.encode(data), {
        ['Content-Type'] = 'application/json'
    })
end

-- Hàm chuyển job về off-duty trong game
local function SwitchToOffDutyJob(xPlayer, currentJob)
    local offDutyJob = nil
    local grade = xPlayer.job.grade

    -- Map on-duty jobs to off-duty jobs
    local jobMapping = {
        ['police'] = 'offpolice',
        ['police2'] = 'offpolice2',
        ['ambulance'] = 'offambulance',
        ['mechanic'] = 'offmechanic',
        ['tiembanh'] = 'offtiembanh',
        ['army'] = 'offarmy'
    }

    offDutyJob = jobMapping[currentJob]

    if offDutyJob then
        -- Chuyển job về off-duty
        xPlayer.setJob(offDutyJob, grade)
        if CONFIG.enableLogging then
            print(('[Duty Integration] 🔄 Switched %s from %s to %s (grade %d)'):format(xPlayer.getName(), currentJob, offDutyJob, grade))
        end
        return true
    end

    return false
end

-- Xử lý khi player disconnect - TỰ ĐỘNG OFF DUTY (cả web và game)
AddEventHandler('playerDropped', function(reason)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if xPlayer then
        local identifier = xPlayer.identifier
        local name = GetPlayerName(_source)
        local job = xPlayer.job.name

        -- Chỉ xử lý cho các job đang on-duty
        local onDutyJobs = { 'police', 'police2', 'ambulance', 'mechanic', 'tiembanh', 'army' }

        local isOnDutyJob = false
        for _, onDutyJob in ipairs(onDutyJobs) do
            if job == onDutyJob then
                isOnDutyJob = true
                break
            end
        end

        if isOnDutyJob then
            if CONFIG.enableLogging then
                print(('[Duty Integration] 🚪 Player %s (%s) disconnected while on duty job: %s - Processing auto off-duty...'):format(name, identifier, job))
            end

            -- 1. Chuyển job về off-duty trong game (sẽ lưu vào database)
            local switched = SwitchToOffDutyJob(xPlayer, job)

            -- 2. Gửi thông tin disconnect để off duty trên web
            SendPlayerDisconnect(identifier, name, reason)

            -- 3. Ghi log off_duty vào database local (nếu có)
            if switched then
                LogDutyStatus(identifier, name, job, xPlayer.job.grade, 'off_duty')
                SendDiscordLog(identifier, name, job, xPlayer.job.grade, 'off_duty')
                SendToNodeServer(identifier, name, job, xPlayer.job.grade, 'off_duty')
            end

            if CONFIG.enableLogging then
                print(('[Duty Integration] ✅ Auto off-duty completed for %s - Job switched: %s'):format(name, switched and 'YES' or 'NO'))
            end
        else
            if CONFIG.enableLogging then
                print(('[Duty Integration] ℹ️ Player %s disconnected with off-duty job: %s - No action needed'):format(name, job))
            end
        end
    else
        if CONFIG.enableLogging then
            print(('[Duty Integration] ⚠️ Could not get xPlayer for source %s on disconnect'):format(_source))
        end
    end
end)

-- Command để test gửi dữ liệu (chỉ dành cho admin)
RegisterCommand('testduty', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer and xPlayer.getGroup() == 'admin' then
        local testData = {
            identifier = 'test:' .. source,
            name = GetPlayerName(source),
            job = 'police',
            grade = 1,
            status = args[1] or 'on_duty'
        }

        SendToNodeServer(testData.identifier, testData.name, testData.job, testData.grade, testData.status)
        TriggerClientEvent('chat:addMessage', source, {
            color = { 0, 255, 0 },
            multiline = true,
            args = { "System", "Test duty data sent to Node.js server!" }
        })
    end
end, false)

-- Event xử lý khi player mất job duty
RegisterServerEvent('duty:jobLost')
AddEventHandler('duty:jobLost', function(identifier, name, oldJob, oldGrade, reason)
    local _source = source

    if CONFIG.enableLogging then
        print(('[Duty Integration] 💼 Player %s (%s) lost duty job: %s - Auto off duty processing...'):format(name, identifier, oldJob))
    end

    -- Ghi log off_duty vào các hệ thống
    LogDutyStatus(identifier, name, oldJob, oldGrade, 'off_duty')
    SendDiscordLog(identifier, name, oldJob, oldGrade, 'off_duty')
    SendToNodeServer(identifier, name, oldJob, oldGrade, 'off_duty')

    -- Gửi thông tin job lost để off duty trên web
    SendPlayerDisconnect(identifier, name, reason or 'job_lost')

    if CONFIG.enableLogging then
        print(('[Duty Integration] ✅ Job lost auto off-duty completed for %s'):format(name))
    end
end)

-- Command để test disconnect (admin only)
RegisterCommand('testdisconnect', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer and xPlayer.getGroup() == 'admin' then
        local identifier = xPlayer.identifier
        local name = GetPlayerName(source)

        SendPlayerDisconnect(identifier, name, 'test_disconnect')
        TriggerClientEvent('chat:addMessage', source, {
            color = { 255, 165, 0 },
            multiline = true,
            args = { "System", "Test disconnect sent to Node.js server!" }
        })
    end
end, false)

-- Command để test job lost (admin only)
RegisterCommand('testjoblost', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer and xPlayer.getGroup() == 'admin' then
        local identifier = xPlayer.identifier
        local name = GetPlayerName(source)
        local job = args[1] or 'police'
        local grade = tonumber(args[2]) or 1

        TriggerEvent('duty:jobLost', identifier, name, job, grade, 'test_job_lost')
        TriggerClientEvent('chat:addMessage', source, {
            color = { 255, 165, 0 },
            multiline = true,
            args = { "System", "Test job lost sent for job: " .. job }
        })
    end
end, false)

if CONFIG.enableLogging then
    print('[Duty Integration] 🚀 FiveM-NodeJS Integration loaded successfully!')
    print('[Duty Integration] 📊 Node.js Server URL: ' .. CONFIG.nodeServerUrl)
    print('[Duty Integration] 🔧 Features enabled:')
    print('  - Duty logging to Node.js server')
    print('  - Discord webhook integration')
    print('  - Auto disconnect handling')
    print('  - Database logging (if MySQL available)')
    print('[Duty Integration] 💻 Admin commands:')
    print('  - /testduty [on_duty|off_duty] - Test duty logging')
    print('  - /testdisconnect - Test disconnect handling')
end