// Test UI fixes - badge colors và gỡ force off-duty
const axios = require('axios');

const BASE_URL = 'https://med.kaitomc.site/api';

async function testUIFixes() {
    console.log('🎨 Testing UI Fixes - Badge Colors & Removed Force Off-Duty...\n');

    try {
        // 1. Tạo test data với cả on_duty và off_duty
        console.log('1. Creating test data with both on_duty and off_duty...');
        
        const testPlayers = [
            {
                identifier: 'steam:ui_test_on_duty',
                name: 'UI Test On Duty',
                job: 'police',
                grade: 1,
                status: 'on_duty'
            },
            {
                identifier: 'steam:ui_test_off_duty',
                name: 'UI Test Off Duty',
                job: 'ambulance',
                grade: 2,
                status: 'off_duty'
            }
        ];

        for (const player of testPlayers) {
            const response = await axios.post(`${BASE_URL}/fivem/duty-log`, player);
            console.log(`✅ ${player.name} (${player.status}):`, response.data.message);
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // 2. <PERSON><PERSON><PERSON> tra duty logs
        console.log('\n2. Checking duty logs for badge display...');
        const logsResponse = await axios.get(`${BASE_URL}/fivem/duty-logs?limit=10`);
        console.log('✅ Recent duty logs:');
        
        let foundOnDuty = false;
        let foundOffDuty = false;
        
        logsResponse.data.data.slice(0, 10).forEach((log, index) => {
            const time = new Date(log.timestamp).toLocaleString();
            const statusEmoji = log.status === 'on_duty' ? '🟢' : '🔴';
            const statusText = log.status === 'on_duty' ? 'ON DUTY' : 'OFF DUTY';
            
            if (log.status === 'on_duty') foundOnDuty = true;
            if (log.status === 'off_duty') foundOffDuty = true;
            
            console.log(`   ${index + 1}. ${statusEmoji} ${log.name} (${log.job}) - ${statusText} - ${time}`);
        });

        console.log(`\n📊 Badge test data:`);
        console.log(`   - Found ON DUTY logs: ${foundOnDuty ? '✅' : '❌'}`);
        console.log(`   - Found OFF DUTY logs: ${foundOffDuty ? '✅' : '❌'}`);

        // 3. Kiểm tra active sessions
        console.log('\n3. Checking active sessions (should not have Force Off buttons)...');
        const activeResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse.data.total}`);
        
        if (activeResponse.data.data && activeResponse.data.data.length > 0) {
            console.log('   Current active sessions:');
            activeResponse.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
            });
        }

        // 4. Test cleanup để dọn dẹp test data
        console.log('\n4. Cleaning up test data...');
        
        for (const player of testPlayers) {
            if (player.status === 'on_duty') {
                // End session for on_duty player
                const disconnectData = {
                    identifier: player.identifier,
                    name: player.name,
                    reason: 'cleanup_test'
                };
                
                const disconnectResponse = await axios.post(`${BASE_URL}/fivem/player-disconnect`, disconnectData);
                console.log(`✅ Cleaned up ${player.name}:`, disconnectResponse.data.message);
            }
        }

        console.log('\n🎉 UI Fixes Test Completed!');
        console.log('\n📋 Changes Made:');
        console.log('✅ 1. Fixed badge colors for duty status:');
        console.log('   - ON DUTY: Green badge (bg-success) with 🟢');
        console.log('   - OFF DUTY: Red badge (bg-danger) with 🔴');
        console.log('✅ 2. Removed Force Off-Duty feature:');
        console.log('   - Removed Force Off button from Active Sessions');
        console.log('   - Removed forceOffDuty() function');
        console.log('   - Changed column header to "Ghi chú"');
        console.log('   - Added info badge "Tự động off khi disconnect"');
        console.log('✅ 3. Removed auto off-duty on disconnect:');
        console.log('   - Removed playerDropped event handler');
        console.log('   - Removed SendPlayerDisconnect function');

        console.log('\n🌐 Web Dashboard Changes:');
        console.log('📊 Duty Logs Tab:');
        console.log('   - ON DUTY entries: Green badge with 🟢 icon');
        console.log('   - OFF DUTY entries: Red badge with 🔴 icon');
        console.log('   - Proper Bootstrap badge classes (bg-success, bg-danger)');
        console.log('');
        console.log('👥 Active Sessions Tab:');
        console.log('   - No more "Force Off" buttons');
        console.log('   - "Ghi chú" column with info about auto off-duty');
        console.log('   - Clean interface without admin controls');

        console.log('\n🔧 Next Steps:');
        console.log('1. Refresh web dashboard to see badge color fixes');
        console.log('2. Check Duty Logs tab - colors should display correctly');
        console.log('3. Check Active Sessions tab - no Force Off buttons');
        console.log('4. Update FiveM server.lua if needed');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy test
testUIFixes();
