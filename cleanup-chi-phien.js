// Script để cleanup session c<PERSON><PERSON> "<PERSON><PERSON> Phiến" 
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function cleanupChiPhien() {
    console.log('🧹 Cleaning up "Ch<PERSON> Phiến" session...\n');

    try {
        // 1. Ki<PERSON>m tra active sessions hiện tại
        console.log('1. Checking current active sessions...');
        const activeResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Current active sessions: ${activeResponse.data.total}`);
        
        if (activeResponse.data.total > 0) {
            console.log('   Active sessions:');
            activeResponse.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
            });
        }

        // 2. Simulate disconnect cho "Chị Phiến"
        console.log('\n2. Processing disconnect for "Ch<PERSON> Phiến"...');
        const disconnectData = {
            identifier: 'steam:1100001417d37d0',
            name: '<PERSON><PERSON>ến',
            reason: 'manual_cleanup'
        };

        const disconnect<PERSON>esponse = await axios.post(`${BASE_URL}/fivem/player-disconnect`, disconnectData);
        console.log('✅ Disconnect processed:', disconnectResponse.data.message);
        
        if (disconnectResponse.data.data) {
            console.log('📊 Sessions ended:', disconnectResponse.data.data.sessionsEnded);
        }

        // 3. Kiểm tra active sessions sau cleanup
        console.log('\n3. Checking active sessions after cleanup...');
        const activeResponse2 = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions after cleanup: ${activeResponse2.data.total}`);
        
        if (activeResponse2.data.total === 0) {
            console.log('🎉 SUCCESS! No active sessions remaining');
        } else {
            console.log('   Remaining active sessions:');
            activeResponse2.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
            });
        }

        // 4. Cleanup old sessions (older than 1 hour)
        console.log('\n4. Running cleanup for old sessions...');
        const cleanupResponse = await axios.post(`${BASE_URL}/fivem/cleanup-sessions`, {
            maxHours: 1
        });
        console.log('✅ Cleanup response:', cleanupResponse.data.message);
        
        if (cleanupResponse.data.data) {
            console.log('📊 Sessions cleaned:', cleanupResponse.data.data.sessionsEnded);
        }

        console.log('\n🎉 Cleanup completed!');
        console.log('\n📋 What happened:');
        console.log('✅ 1. Ended any active session for "Chị Phiến"');
        console.log('✅ 2. Cleaned up old sessions');
        console.log('✅ 3. Web dashboard should now show correct data');
        
        console.log('\n🌐 Refresh the web dashboard:');
        console.log('   - "Đang Online" tab should be empty or not show "Chị Phiến"');
        console.log('   - "Phiên Duty" tab should show completed sessions');

    } catch (error) {
        console.error('❌ Cleanup failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy cleanup
cleanupChiPhien();
