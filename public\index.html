<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FiveM Duty Logs Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .stat-card.danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .badge-on-duty {
            background-color: #28a745;
        }
        .badge-off-duty {
            background-color: #dc3545;
        }
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        .job-badge {
            font-size: 0.8em;
            padding: 0.3em 0.6em;
        }
        .loading {
            display: none;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        .job-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .job-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        .job-card.active {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }
        .job-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .job-police { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); }
        .job-police2 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .job-ambulance { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .job-mechanic { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .job-tiembanh { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .job-army { background: linear-gradient(135deg, #8360c3 0%, #2ebf91 100%); }
        .job-default { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-clipboard-check me-2"></i>
                FiveM Duty Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="lastUpdate">
                    Cập nhật lần cuối: <span id="updateTime">--</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Job Selection Cards -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-briefcase me-2"></i>
                            Chọn Job để xem thống kê
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="jobCards">
                            <!-- Job cards will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card info">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4 id="totalLogs">0</h4>
                        <p class="mb-0">Tổng Logs</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card success">
                    <div class="card-body text-center">
                        <i class="fas fa-user-check fa-2x mb-2"></i>
                        <h4 id="onDutyCount">0</h4>
                        <p class="mb-0">On Duty</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card danger">
                    <div class="card-body text-center">
                        <i class="fas fa-user-times fa-2x mb-2"></i>
                        <h4 id="offDutyCount">0</h4>
                        <p class="mb-0">Off Duty</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-briefcase fa-2x mb-2"></i>
                        <h4 id="activeJobs">0</h4>
                        <p class="mb-0">Jobs Hoạt động</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    Bộ lọc và Chọn ngày
                </h5>
            </div>
            <div class="card-body">
                <!-- Date Range Selection -->
                <div class="row mb-3">
                    <div class="col-12">
                        <label class="form-label fw-bold">
                            <i class="fas fa-calendar-alt me-2"></i>Chọn khoảng thời gian
                        </label>
                        <div class="btn-group d-block" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm me-2 mb-2" onclick="setDateRange('today')">
                                <i class="fas fa-calendar-day me-1"></i>Hôm nay
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm me-2 mb-2" onclick="setDateRange('yesterday')">
                                <i class="fas fa-calendar-minus me-1"></i>Hôm qua
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm me-2 mb-2" onclick="setDateRange('week')">
                                <i class="fas fa-calendar-week me-1"></i>7 ngày qua
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm me-2 mb-2" onclick="setDateRange('month')">
                                <i class="fas fa-calendar me-1"></i>30 ngày qua
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm mb-2" onclick="setDateRange('all')">
                                <i class="fas fa-infinity me-1"></i>Tất cả
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Custom Date Range -->
                <div class="row mb-3">
                    <div class="col-md-4 mb-2">
                        <label for="filterDateFrom" class="form-label">Từ ngày</label>
                        <input type="date" class="form-control" id="filterDateFrom">
                    </div>
                    <div class="col-md-4 mb-2">
                        <label for="filterDateTo" class="form-label">Đến ngày</label>
                        <input type="date" class="form-control" id="filterDateTo">
                    </div>
                    <div class="col-md-4 mb-2">
                        <label for="filterDate" class="form-label">Ngày cụ thể</label>
                        <input type="date" class="form-control" id="filterDate">
                        <small class="text-muted">Để trống để sử dụng khoảng thời gian</small>
                    </div>
                </div>

                <!-- Other Filters -->
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <label for="filterJob" class="form-label">Job</label>
                        <select class="form-select" id="filterJob">
                            <option value="">Tất cả Jobs</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="filterStatus" class="form-label">Trạng thái</label>
                        <select class="form-select" id="filterStatus">
                            <option value="">Tất cả</option>
                            <option value="on_duty">On Duty</option>
                            <option value="off_duty">Off Duty</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="filterPlayer" class="form-label">Player</label>
                        <input type="text" class="form-control" id="filterPlayer" placeholder="Tên hoặc identifier">
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="filterLimit" class="form-label">Giới hạn</label>
                        <select class="form-select" id="filterLimit">
                            <option value="">Tất cả</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                            <option value="200">200</option>
                            <option value="500">500</option>
                        </select>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="row mt-3">
                    <div class="col-12">
                        <button class="btn btn-primary" onclick="loadDutyLogs()">
                            <i class="fas fa-search me-2"></i>Tìm kiếm
                        </button>
                        <button class="btn btn-secondary ms-2" onclick="clearFilters()">
                            <i class="fas fa-times me-2"></i>Xóa bộ lọc
                        </button>
                        <button class="btn btn-success ms-2" onclick="exportData()">
                            <i class="fas fa-download me-2"></i>Xuất Excel
                        </button>
                        <div class="float-end">
                            <small class="text-muted" id="filterInfo">
                                <i class="fas fa-info-circle me-1"></i>
                                Hiển thị: <span id="currentFilter">Tất cả dữ liệu</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
                    <i class="fas fa-list me-2"></i>Duty Logs
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" type="button" role="tab">
                    <i class="fas fa-chart-bar me-2"></i>Thống kê Players
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabContent">
            <!-- Duty Logs Tab -->
            <div class="tab-pane fade show active" id="logs" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Duty Logs
                        </h5>
                        <div class="loading">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th>Thời gian</th>
                                <th>Tên</th>
                                <th>Job</th>
                                <th>Grade</th>
                                <th>Trạng thái</th>
                                <th>Identifier</th>
                            </tr>
                        </thead>
                        <tbody id="dutyLogsTable">
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Chưa có dữ liệu. Nhấn "Tìm kiếm" để tải dữ liệu.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Player Statistics Tab -->
    <div class="tab-pane fade" id="stats" role="tabpanel">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Thống kê thời gian online của Players
                </h5>
                <div class="loading-stats">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th>Player</th>
                                <th>Job</th>
                                <th>Tổng Sessions</th>
                                <th>Tổng thời gian</th>
                                <th>Trung bình/Session</th>
                                <th>Lần cuối On Duty</th>
                                <th>Lần cuối Off Duty</th>
                            </tr>
                        </thead>
                        <tbody id="playerStatsTable">
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Chưa có dữ liệu. Nhấn tab để tải dữ liệu.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

    <!-- Refresh Button -->
    <button class="btn btn-primary btn-lg refresh-btn" onclick="loadDutyLogs()" title="Refresh">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
