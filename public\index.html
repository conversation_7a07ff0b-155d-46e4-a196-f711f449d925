<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FiveM Duty Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-radius: 16px;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #374151;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            margin: 20px;
            min-height: calc(100vh - 40px);
            box-shadow: var(--shadow-xl);
        }

        .navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: none;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            padding: 1.5rem 2rem;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .navbar-text {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
            padding: 1.5rem 2rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        .stat-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .stat-card.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
        }

        .stat-card.danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
        }

        .stat-card.warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
        }

        .stat-card.info {
            background: linear-gradient(135deg, var(--info-color) 0%, #2563eb 100%);
        }

        .stat-card .card-body {
            position: relative;
            z-index: 1;
            padding: 2rem;
        }

        .stat-card h4 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            font-weight: 500;
            opacity: 0.9;
        }

        .stat-card i {
            opacity: 0.8;
        }

        .job-card {
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .job-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .job-card:hover::before {
            opacity: 1;
        }

        .job-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-xl);
        }

        .job-card.active {
            border-color: white;
            box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.3);
        }

        .job-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
        }

        .job-police { background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%); }
        .job-police2 { background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%); }
        .job-ambulance { background: linear-gradient(135deg, #059669 0%, #047857 100%); }
        .job-mechanic { background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); }
        .job-tiembanh { background: linear-gradient(135deg, #d97706 0%, #b45309 100%); }
        .job-army { background: linear-gradient(135deg, #374151 0%, #1f2937 100%); }
        .job-default { background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); }

        .nav-tabs {
            border: none;
            background: #f8fafc;
            border-radius: var(--border-radius);
            padding: 0.5rem;
            margin-bottom: 2rem;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: calc(var(--border-radius) - 4px);
            color: #6b7280;
            font-weight: 500;
            padding: 1rem 1.5rem;
            transition: all 0.3s ease;
            margin: 0 0.25rem;
        }

        .nav-tabs .nav-link:hover {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }

        .nav-tabs .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: none;
            font-weight: 600;
            color: var(--dark-color);
            padding: 1rem 1.5rem;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background: rgba(99, 102, 241, 0.05);
            transform: scale(1.01);
        }

        .table tbody td {
            padding: 1rem 1.5rem;
            border: none;
            border-bottom: 1px solid #f1f5f9;
        }

        .badge {
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.875rem;
        }

        .btn {
            border-radius: 50px;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
        }

        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .loading, .loading-stats, .loading-sessions, .loading-active {
            display: none;
        }

        .spinner-border {
            color: var(--primary-color);
        }

        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.1) rotate(180deg);
            box-shadow: var(--shadow-xl);
        }

        .table-container {
            max-height: 70vh;
            overflow-y: auto;
            border-radius: var(--border-radius);
        }

        .table-container::-webkit-scrollbar {
            width: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .alert {
            border: none;
            border-radius: var(--border-radius);
            padding: 1.5rem;
        }

        .toast {
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                min-height: calc(100vh - 20px);
            }

            .content-wrapper {
                padding: 1rem;
            }

            .navbar {
                padding: 1rem;
            }

            .job-icon {
                font-size: 2rem;
            }

            .stat-card h4 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Navigation -->
        <nav class="navbar">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-shield-alt me-3"></i>
                    FiveM Duty Dashboard
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text" id="lastUpdate">
                        <i class="fas fa-clock me-2"></i>
                        Cập nhật: <span id="updateTime">--</span>
                    </span>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
        <!-- Job Selection Cards -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users-cog me-2"></i>
                            Chọn Job để xem thống kê
                        </h5>
                        <small class="text-muted">Click vào job card để lọc dữ liệu theo nghề nghiệp</small>
                    </div>
                    <div class="card-body">
                        <div class="row g-3" id="jobCards">
                            <!-- Job cards will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card info">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4 id="totalLogs">0</h4>
                        <p class="mb-0">Tổng Logs</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card success">
                    <div class="card-body text-center">
                        <i class="fas fa-user-check fa-2x mb-2"></i>
                        <h4 id="onDutyCount">0</h4>
                        <p class="mb-0">On Duty</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card danger">
                    <div class="card-body text-center">
                        <i class="fas fa-user-times fa-2x mb-2"></i>
                        <h4 id="offDutyCount">0</h4>
                        <p class="mb-0">Off Duty</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-briefcase fa-2x mb-2"></i>
                        <h4 id="activeJobs">0</h4>
                        <p class="mb-0">Jobs Hoạt động</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-5">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sliders-h me-2"></i>
                    Bộ lọc và Tùy chọn hiển thị
                </h5>
                <small class="text-muted">Sử dụng các bộ lọc để tìm kiếm dữ liệu cụ thể</small>
            </div>
            <div class="card-body">
                <!-- Date Range Selection -->
                <div class="row mb-4">
                    <div class="col-12">
                        <label class="form-label fw-bold mb-3">
                            <i class="fas fa-calendar-alt me-2 text-primary"></i>Chọn khoảng thời gian
                        </label>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn-outline-primary" onclick="setDateRange('today')">
                                <i class="fas fa-calendar-day me-2"></i>Hôm nay
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="setDateRange('yesterday')">
                                <i class="fas fa-calendar-minus me-2"></i>Hôm qua
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="setDateRange('week')">
                                <i class="fas fa-calendar-week me-2"></i>7 ngày qua
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="setDateRange('month')">
                                <i class="fas fa-calendar me-2"></i>30 ngày qua
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('all')">
                                <i class="fas fa-infinity me-2"></i>Tất cả
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Custom Date Range -->
                <div class="row mb-4">
                    <div class="col-md-4 mb-3">
                        <label for="filterDateFrom" class="form-label fw-semibold">
                            <i class="fas fa-calendar-plus me-2 text-success"></i>Từ ngày
                        </label>
                        <input type="date" class="form-control" id="filterDateFrom">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="filterDateTo" class="form-label fw-semibold">
                            <i class="fas fa-calendar-minus me-2 text-danger"></i>Đến ngày
                        </label>
                        <input type="date" class="form-control" id="filterDateTo">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="filterDate" class="form-label fw-semibold">
                            <i class="fas fa-calendar-check me-2 text-info"></i>Ngày cụ thể
                        </label>
                        <input type="date" class="form-control" id="filterDate">
                        <small class="text-muted mt-1 d-block">
                            <i class="fas fa-info-circle me-1"></i>Để trống để sử dụng khoảng thời gian
                        </small>
                    </div>
                </div>

                <!-- Other Filters -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <label for="filterJob" class="form-label fw-semibold">
                            <i class="fas fa-briefcase me-2 text-primary"></i>Nghề nghiệp
                        </label>
                        <select class="form-select" id="filterJob">
                            <option value="">Tất cả Jobs</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="filterStatus" class="form-label fw-semibold">
                            <i class="fas fa-toggle-on me-2 text-warning"></i>Trạng thái
                        </label>
                        <select class="form-select" id="filterStatus">
                            <option value="">Tất cả trạng thái</option>
                            <option value="on_duty">🟢 On Duty</option>
                            <option value="off_duty">🔴 Off Duty</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="filterPlayer" class="form-label fw-semibold">
                            <i class="fas fa-user-search me-2 text-info"></i>Tìm Player
                        </label>
                        <input type="text" class="form-control" id="filterPlayer" placeholder="Nhập tên hoặc identifier...">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="filterLimit" class="form-label fw-semibold">
                            <i class="fas fa-list-ol me-2 text-secondary"></i>Giới hạn hiển thị
                        </label>
                        <select class="form-select" id="filterLimit">
                            <option value="">Tất cả</option>
                            <option value="50">50 records</option>
                            <option value="100">100 records</option>
                            <option value="200">200 records</option>
                            <option value="500">500 records</option>
                        </select>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex flex-wrap gap-3 align-items-center justify-content-between">
                            <div class="d-flex flex-wrap gap-2">
                                <button class="btn btn-primary" onclick="loadDutyLogs()">
                                    <i class="fas fa-search me-2"></i>Tìm kiếm
                                </button>
                                <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                    <i class="fas fa-eraser me-2"></i>Xóa bộ lọc
                                </button>
                                <button class="btn btn-success" onclick="exportData()">
                                    <i class="fas fa-file-excel me-2"></i>Xuất Excel
                                </button>
                            </div>
                            <div class="text-end">
                                <div class="badge bg-light text-dark fs-6 px-3 py-2" id="filterInfo">
                                    <i class="fas fa-filter me-2"></i>
                                    <span id="currentFilter">Tất cả dữ liệu</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs mb-5" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
                    <i class="fas fa-clipboard-list me-2"></i>
                    <span class="d-none d-sm-inline">Duty Logs</span>
                    <span class="d-sm-none">Logs</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" type="button" role="tab">
                    <i class="fas fa-chart-pie me-2"></i>
                    <span class="d-none d-sm-inline">Thống kê Players</span>
                    <span class="d-sm-none">Stats</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sessions-tab" data-bs-toggle="tab" data-bs-target="#sessions" type="button" role="tab">
                    <i class="fas fa-history me-2"></i>
                    <span class="d-none d-sm-inline">Phiên Duty</span>
                    <span class="d-sm-none">Sessions</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="active-tab" data-bs-toggle="tab" data-bs-target="#active" type="button" role="tab">
                    <i class="fas fa-users me-2"></i>
                    <span class="d-none d-sm-inline">Đang Online</span>
                    <span class="d-sm-none">Online</span>
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabContent">
            <!-- Duty Logs Tab -->
            <div class="tab-pane fade show active" id="logs" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Duty Logs
                        </h5>
                        <div class="loading">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th>Thời gian</th>
                                <th>Tên</th>
                                <th>Job</th>
                                <th>Grade</th>
                                <th>Trạng thái</th>
                                <th>Identifier</th>
                            </tr>
                        </thead>
                        <tbody id="dutyLogsTable">
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Chưa có dữ liệu. Nhấn "Tìm kiếm" để tải dữ liệu.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Player Statistics Tab -->
    <div class="tab-pane fade" id="stats" role="tabpanel">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Thống kê thời gian online của Players
                </h5>
                <div class="loading-stats">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th>Player</th>
                                <th>Job</th>
                                <th>Tổng Sessions</th>
                                <th>Tổng thời gian</th>
                                <th>Thời gian định dạng</th>
                                <th>Trung bình/Session</th>
                                <th>Lần cuối On Duty</th>
                                <th>Lần cuối Off Duty</th>
                            </tr>
                        </thead>
                        <tbody id="playerStatsTable">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Chưa có dữ liệu. Nhấn tab để tải dữ liệu.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Duty Sessions Tab -->
    <div class="tab-pane fade" id="sessions" role="tabpanel">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Chi tiết các phiên Duty (Thời gian cụ thể On → Off)
                </h5>
                <div class="loading-sessions">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th>Player</th>
                                <th>Job</th>
                                <th>Grade</th>
                                <th>Bắt đầu (On Duty)</th>
                                <th>Kết thúc (Off Duty)</th>
                                <th>Thời gian Duty</th>
                                <th>Trạng thái</th>
                                <th>Ngày</th>
                            </tr>
                        </thead>
                        <tbody id="dutySessionsTable">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Chưa có dữ liệu. Nhấn tab để tải dữ liệu.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Sessions Tab -->
    <div class="tab-pane fade" id="active" role="tabpanel">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-play-circle me-2"></i>
                    Players đang Online Duty (Chưa Off Duty)
                </h5>
                <div class="d-flex align-items-center">
                    <button class="btn btn-warning btn-sm me-2" onclick="cleanupOldSessions()">
                        <i class="fas fa-broom me-1"></i>Cleanup Sessions Cũ
                    </button>
                    <div class="loading-active">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="alert alert-info m-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Lưu ý:</strong> Đây là danh sách những người đang on duty nhưng chưa off duty.
                    Nếu họ thoát game mà không off duty, hệ thống sẽ tự động kết thúc session.
                </div>
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th>Player</th>
                                <th>Job</th>
                                <th>Grade</th>
                                <th>Bắt đầu Duty</th>
                                <th>Thời gian đã Online</th>
                                <th>Trạng thái</th>
                                <th>Ghi chú</th>
                            </tr>
                        </thead>
                        <tbody id="activeSessionsTable">
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Chưa có dữ liệu. Nhấn tab để tải dữ liệu.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="loadDutyLogs()" title="Refresh Data">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
