<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FiveM Duty Logs Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .stat-card.danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .badge-on-duty {
            background-color: #28a745;
        }
        .badge-off-duty {
            background-color: #dc3545;
        }
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        .job-badge {
            font-size: 0.8em;
            padding: 0.3em 0.6em;
        }
        .loading {
            display: none;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-clipboard-check me-2"></i>
                FiveM Duty Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="lastUpdate">
                    Cập nhật lần cuối: <span id="updateTime">--</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card info">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4 id="totalLogs">0</h4>
                        <p class="mb-0">Tổng Logs</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card success">
                    <div class="card-body text-center">
                        <i class="fas fa-user-check fa-2x mb-2"></i>
                        <h4 id="onDutyCount">0</h4>
                        <p class="mb-0">On Duty</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card danger">
                    <div class="card-body text-center">
                        <i class="fas fa-user-times fa-2x mb-2"></i>
                        <h4 id="offDutyCount">0</h4>
                        <p class="mb-0">Off Duty</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-briefcase fa-2x mb-2"></i>
                        <h4 id="jobCount">0</h4>
                        <p class="mb-0">Jobs</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    Bộ lọc
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <label for="filterJob" class="form-label">Job</label>
                        <select class="form-select" id="filterJob">
                            <option value="">Tất cả Jobs</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="filterStatus" class="form-label">Trạng thái</label>
                        <select class="form-select" id="filterStatus">
                            <option value="">Tất cả</option>
                            <option value="on_duty">On Duty</option>
                            <option value="off_duty">Off Duty</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="filterDate" class="form-label">Ngày</label>
                        <input type="date" class="form-control" id="filterDate">
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="filterLimit" class="form-label">Giới hạn</label>
                        <select class="form-select" id="filterLimit">
                            <option value="">Tất cả</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                            <option value="200">200</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <button class="btn btn-primary" onclick="loadDutyLogs()">
                            <i class="fas fa-search me-2"></i>Tìm kiếm
                        </button>
                        <button class="btn btn-secondary ms-2" onclick="clearFilters()">
                            <i class="fas fa-times me-2"></i>Xóa bộ lọc
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Duty Logs Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Duty Logs
                </h5>
                <div class="loading">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th>Thời gian</th>
                                <th>Tên</th>
                                <th>Job</th>
                                <th>Grade</th>
                                <th>Trạng thái</th>
                                <th>Identifier</th>
                            </tr>
                        </thead>
                        <tbody id="dutyLogsTable">
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Chưa có dữ liệu. Nhấn "Tìm kiếm" để tải dữ liệu.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="btn btn-primary btn-lg refresh-btn" onclick="loadDutyLogs()" title="Refresh">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
