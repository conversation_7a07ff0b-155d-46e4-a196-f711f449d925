// Test script để kiểm tra xử lý reconnect và duty status check
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testReconnectHandling() {
    console.log('🚀 Testing Reconnect Handling & Duty Status Check...\n');

    try {
        // Test case 1: Tạo player on duty tr<PERSON><PERSON><PERSON> khi "disconnect"
        console.log('1. Setting up test scenario - Player goes on duty...');
        
        const testPlayer = {
            identifier: 'steam:reconnect_test_123',
            name: 'Test Player - Reconnect',
            job: 'police',
            grade: 2
        };

        // Player on duty
        await axios.post(`${BASE_URL}/fivem/duty-log`, {
            ...testPlayer,
            status: 'on_duty'
        });
        console.log(`✅ ${testPlayer.name} is now on duty`);
        console.log('');

        // Simulate some time passing (player is working)
        console.log('2. Simulating player working for some time...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('✅ Player has been working...');
        console.log('');

        // Test case 2: Player "disconnects" (but we don't call disconnect endpoint)
        console.log('3. Player disconnects without proper off-duty (simulated)...');
        console.log('   (In real scenario, player just closes game without off-duty)');
        console.log('');

        // Test case 3: Player "reconnects" - check duty status
        console.log('4. Player reconnects - Checking duty status...');
        const dutyStatusResponse = await axios.post(`${BASE_URL}/fivem/check-duty-status`, {
            identifier: testPlayer.identifier,
            name: testPlayer.name
        });
        
        console.log('✅ Duty status check response:');
        const dutyData = dutyStatusResponse.data.data;
        console.log('   - Has active sessions:', dutyData.hasActiveSessions);
        console.log('   - Active sessions count:', dutyData.activeSessions.length);
        
        if (dutyData.activeSessions.length > 0) {
            const session = dutyData.activeSessions[0];
            const startTime = new Date(session.start_time);
            const now = new Date();
            const minutesActive = Math.floor((now - startTime) / (1000 * 60));
            
            console.log('   - Session details:');
            console.log(`     * Job: ${session.job}`);
            console.log(`     * Grade: ${session.grade}`);
            console.log(`     * Started: ${startTime.toLocaleString()}`);
            console.log(`     * Active for: ${minutesActive} minutes`);
        }
        
        console.log('   - Recommendations:');
        dutyData.recommendations.forEach((rec, index) => {
            console.log(`     ${index + 1}. ${rec.message}`);
            console.log(`        Actions: ${rec.actions.join(', ')}`);
        });
        console.log('');

        // Test case 4: Test different reconnect actions
        const reconnectActions = [
            {
                action: 'continue',
                description: 'Continue old session'
            },
            {
                action: 'end_session',
                description: 'End old session'
            },
            {
                action: 'end_and_new',
                description: 'End old and start new session'
            },
            {
                action: 'ignore',
                description: 'Ignore old session'
            }
        ];

        for (let i = 0; i < reconnectActions.length; i++) {
            const testAction = reconnectActions[i];
            
            console.log(`5.${i + 1}. Testing reconnect action: ${testAction.action}`);
            
            // Create a new test player for each action
            const actionTestPlayer = {
                identifier: `steam:reconnect_action_${i}`,
                name: `Action Test Player ${i + 1}`,
                job: 'ambulance',
                grade: 1
            };

            // Put player on duty first
            await axios.post(`${BASE_URL}/fivem/duty-log`, {
                ...actionTestPlayer,
                status: 'on_duty'
            });

            await new Promise(resolve => setTimeout(resolve, 500));

            // Test the reconnect action
            const reconnectResponse = await axios.post(`${BASE_URL}/fivem/player-reconnect`, {
                identifier: actionTestPlayer.identifier,
                name: actionTestPlayer.name,
                action: testAction.action
            });

            console.log(`   ✅ ${testAction.description}:`, reconnectResponse.data.data.message);
            
            if (reconnectResponse.data.data.newSession) {
                console.log(`   📝 New session created for job: ${reconnectResponse.data.data.newSession.job}`);
            }
            
            await new Promise(resolve => setTimeout(resolve, 300));
        }
        console.log('');

        // Test case 5: Check active sessions after all tests
        console.log('6. Checking active sessions after reconnect tests...');
        const activeSessionsResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeSessionsResponse.data.total}`);
        
        activeSessionsResponse.data.data.forEach((session, index) => {
            console.log(`   ${index + 1}. ${session.name} (${session.job}) - Active for ${session.time_active}`);
        });
        console.log('');

        // Test case 6: Test với player không có session active
        console.log('7. Testing duty status check for player without active sessions...');
        const cleanPlayer = {
            identifier: 'steam:clean_player_123',
            name: 'Clean Player - No Sessions'
        };

        const cleanStatusResponse = await axios.post(`${BASE_URL}/fivem/check-duty-status`, {
            identifier: cleanPlayer.identifier,
            name: cleanPlayer.name
        });

        console.log('✅ Clean player duty status:');
        const cleanData = cleanStatusResponse.data.data;
        console.log('   - Has active sessions:', cleanData.hasActiveSessions);
        console.log('   - Recommendations count:', cleanData.recommendations.length);
        console.log('');

        // Test case 7: Test với player có nhiều sessions cũ
        console.log('8. Testing player with multiple old sessions...');
        const multiSessionPlayer = {
            identifier: 'steam:multi_session_123',
            name: 'Multi Session Player',
            job: 'mechanic',
            grade: 0
        };

        // Tạo nhiều sessions (on -> off -> on -> off -> on)
        const sessionActions = ['on_duty', 'off_duty', 'on_duty', 'off_duty', 'on_duty'];
        
        for (let i = 0; i < sessionActions.length; i++) {
            await axios.post(`${BASE_URL}/fivem/duty-log`, {
                ...multiSessionPlayer,
                status: sessionActions[i]
            });
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        const multiStatusResponse = await axios.post(`${BASE_URL}/fivem/check-duty-status`, {
            identifier: multiSessionPlayer.identifier,
            name: multiSessionPlayer.name
        });

        console.log('✅ Multi-session player status:');
        const multiData = multiStatusResponse.data.data;
        console.log('   - Has active sessions:', multiData.hasActiveSessions);
        console.log('   - Recent logs count:', multiData.recentLogs.length);
        console.log('   - Player stats count:', multiData.playerStats.length);
        console.log('   - Recommendations:');
        multiData.recommendations.forEach((rec, index) => {
            console.log(`     ${index + 1}. [${rec.type}] ${rec.message}`);
        });
        console.log('');

        console.log('🎉 Reconnect handling test completed successfully!');
        console.log('');
        console.log('✅ Features tested:');
        console.log('   - Duty status check when player joins');
        console.log('   - Detection of active sessions');
        console.log('   - Recommendations based on player history');
        console.log('   - Different reconnect actions (continue, end, end_and_new, ignore)');
        console.log('   - Handling players with no active sessions');
        console.log('   - Handling players with complex session history');
        console.log('');
        console.log('🎮 In FiveM:');
        console.log('   - When player joins, server checks for active sessions');
        console.log('   - If found, client shows menu with options');
        console.log('   - Player can choose to continue, end, or start new session');
        console.log('   - All actions are logged and statistics updated');
        console.log('');
        console.log('🌐 Check the web dashboard:');
        console.log('   - "Đang Online" tab shows current active sessions');
        console.log('   - "Phiên Duty" tab shows all session history');
        console.log('   - Look for different session end reasons in logs');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy test
testReconnectHandling();
