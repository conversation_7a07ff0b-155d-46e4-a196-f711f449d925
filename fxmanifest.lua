fx_version 'cerulean'
game 'gta5'

author 'Your Name'
description 'FiveM Duty System with Node.js Dashboard Integration'
version '2.0.0'

-- Dependencies
dependencies {
    'es_extended',
    'esx_duty'
}

-- Server scripts
server_scripts {
    '@es_extended/locale.lua',
    'locales/en.lua',
    'locales/vi.lua',
    'config.lua',
    'server.lua'
}

-- Client scripts
client_scripts {
    '@es_extended/locale.lua',
    'locales/en.lua', 
    'locales/vi.lua',
    'config.lua',
    'client.lua'
}

-- Exports
exports {
    'isOnDuty',
    'isReconnectMenuOpen',
    'showReconnectMenu'
}

-- Server exports
server_exports {
    'GetPlayerFromIdentifier'
}
