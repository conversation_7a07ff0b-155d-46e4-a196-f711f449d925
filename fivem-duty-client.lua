-- FiveM Duty System - Client Side
-- File này cần được đặt trong thư mục client của resource

-- Biến global
local isReconnectMenuOpen = false

-- Nhận event hiển thị menu reconnect
RegisterNetEvent('duty:showReconnectMenu')
AddEventHandler('duty:showReconnectMenu', function(data)
    if isReconnectMenuOpen then return end
    
    isReconnectMenuOpen = true
    
    local message = data.message
    local session = data.session
    local recommendations = data.recommendations
    
    -- Tạo menu options
    local menuOptions = {
        {
            label = "🔄 Tiếp tục session cũ",
            description = "Tiếp tục làm việc từ session trước",
            action = "continue"
        },
        {
            label = "⏹️ Kết thúc session cũ",
            description = "Kết thúc session và về trạng thái off duty",
            action = "end_session"
        },
        {
            label = "🔄⏹️ Kết thúc và bắt đầu mới",
            description = "Kết thúc session cũ và bắt đầu session mới",
            action = "end_and_new"
        },
        {
            label = "❌ Bỏ qua",
            description = "Bỏ qua session cũ (kh<PERSON>ng khuyến khích)",
            action = "ignore"
        }
    }
    
    -- Hiển thị menu (sử dụng native GTA menu hoặc custom menu)
    ShowReconnectNativeMenu(message, menuOptions)
end)

-- Hàm hiển thị menu native
function ShowReconnectNativeMenu(message, options)
    Citizen.CreateThread(function()
        -- Disable controls while menu is open
        local menuActive = true
        local selectedIndex = 1
        local maxIndex = #options
        
        -- Display instructions
        Citizen.CreateThread(function()
            while menuActive do
                Citizen.Wait(0)
                
                -- Disable all controls
                DisableAllControlActions(0)
                EnableControlAction(0, 1, true) -- LookLeftRight
                EnableControlAction(0, 2, true) -- LookUpDown
                EnableControlAction(0, 172, true) -- Up Arrow
                EnableControlAction(0, 173, true) -- Down Arrow
                EnableControlAction(0, 176, true) -- Enter
                EnableControlAction(0, 177, true) -- Backspace
                
                -- Draw background
                DrawRect(0.5, 0.5, 0.6, 0.8, 0, 0, 0, 200)
                
                -- Draw title
                SetTextFont(4)
                SetTextProportional(1)
                SetTextScale(0.8, 0.8)
                SetTextColour(255, 255, 255, 255)
                SetTextEntry("STRING")
                AddTextComponentString("🔄 DUTY RECONNECT")
                DrawText(0.2, 0.15)
                
                -- Draw message
                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.4, 0.4)
                SetTextColour(255, 255, 255, 255)
                SetTextEntry("STRING")
                AddTextComponentString(message)
                DrawText(0.2, 0.25)
                
                -- Draw options
                for i, option in ipairs(options) do
                    local yPos = 0.4 + (i - 1) * 0.08
                    local color = {255, 255, 255, 255}
                    local bgColor = {50, 50, 50, 100}
                    
                    if i == selectedIndex then
                        color = {0, 0, 0, 255}
                        bgColor = {255, 255, 255, 200}
                    end
                    
                    -- Draw option background
                    DrawRect(0.5, yPos, 0.5, 0.06, bgColor[1], bgColor[2], bgColor[3], bgColor[4])
                    
                    -- Draw option text
                    SetTextFont(0)
                    SetTextProportional(1)
                    SetTextScale(0.4, 0.4)
                    SetTextColour(color[1], color[2], color[3], color[4])
                    SetTextEntry("STRING")
                    AddTextComponentString(option.label)
                    DrawText(0.25, yPos - 0.02)
                    
                    -- Draw description
                    SetTextFont(0)
                    SetTextProportional(1)
                    SetTextScale(0.3, 0.3)
                    SetTextColour(color[1], color[2], color[3], 180)
                    SetTextEntry("STRING")
                    AddTextComponentString(option.description)
                    DrawText(0.25, yPos + 0.015)
                end
                
                -- Draw instructions
                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.3, 0.3)
                SetTextColour(255, 255, 255, 255)
                SetTextEntry("STRING")
                AddTextComponentString("↑↓ Di chuyển | ENTER Chọn | BACKSPACE Hủy")
                DrawText(0.2, 0.75)
                
                -- Handle input
                if IsControlJustPressed(0, 172) then -- Up Arrow
                    selectedIndex = selectedIndex - 1
                    if selectedIndex < 1 then
                        selectedIndex = maxIndex
                    end
                    PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                end
                
                if IsControlJustPressed(0, 173) then -- Down Arrow
                    selectedIndex = selectedIndex + 1
                    if selectedIndex > maxIndex then
                        selectedIndex = 1
                    end
                    PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                end
                
                if IsControlJustPressed(0, 176) then -- Enter
                    local selectedOption = options[selectedIndex]
                    PlaySoundFrontend(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                    
                    -- Send choice to server
                    TriggerServerEvent('duty:handleReconnectChoice', selectedOption.action)
                    
                    menuActive = false
                    isReconnectMenuOpen = false
                    break
                end
                
                if IsControlJustPressed(0, 177) then -- Backspace
                    PlaySoundFrontend(-1, "BACK", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                    
                    -- Default to ignore
                    TriggerServerEvent('duty:handleReconnectChoice', 'ignore')
                    
                    menuActive = false
                    isReconnectMenuOpen = false
                    break
                end
            end
        end)
    end)
end

-- Alternative: Sử dụng chat commands để test
RegisterCommand('testdutymenu', function()
    local testData = {
        message = "Test Menu\n\nBạn có session POLICE đang active từ 2 giờ trước.\n\nBạn muốn:",
        session = {
            job = "police",
            start_time = os.time() - 7200 -- 2 hours ago
        },
        recommendations = {}
    }
    
    TriggerEvent('duty:showReconnectMenu', testData)
end, false)

-- Notification helper
function ShowDutyNotification(message, type)
    type = type or 'info'
    
    -- Sử dụng notification system có sẵn
    if exports['esx_notify'] then
        exports['esx_notify']:Notify(type, 5000, message)
    elseif exports['mythic_notify'] then
        exports['mythic_notify']:DoHudText(type, message)
    else
        -- Fallback to native notification
        SetNotificationTextEntry("STRING")
        AddTextComponentString(message)
        DrawNotification(0, 1)
    end
end

-- Export functions for other resources
exports('showReconnectMenu', function(data)
    TriggerEvent('duty:showReconnectMenu', data)
end)

exports('isReconnectMenuOpen', function()
    return isReconnectMenuOpen
end)
