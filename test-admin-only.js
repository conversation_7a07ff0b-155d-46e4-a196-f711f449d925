// Test admin force off duty only
const axios = require('axios');

const BASE_URL = 'https://med.kaitomc.site/api';

async function testAdminForceOffDuty() {
    console.log('🔧 Testing Admin Force Off Duty...\n');

    try {
        // Create test player on duty for admin test
        const testPlayer = {
            identifier: 'steam:admin_test_999',
            name: 'Admin Test Player',
            job: 'police',
            grade: 2,
            status: 'on_duty'
        };

        console.log('1. Creating test player for admin force off duty...');
        await axios.post(`${BASE_URL}/fivem/duty-log`, testPlayer);
        console.log('✅ Test player on duty created');
        
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if player is in active sessions
        console.log('\n2. Checking active sessions...');
        const activeResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        const foundPlayer = activeResponse.data.data.find(session => 
            session.identifier === testPlayer.identifier
        );
        
        if (foundPlayer) {
            console.log('✅ Test player found in active sessions');
            console.log(`   Player: ${foundPlayer.name} (${foundPlayer.job})`);
            console.log(`   Time active: ${foundPlayer.time_active}`);
        } else {
            console.log('❌ Test player not found in active sessions');
            return;
        }

        // Test admin force off duty
        console.log('\n3. Testing admin force off duty...');
        const adminAction = {
            identifier: testPlayer.identifier,
            adminName: 'Test Admin',
            reason: 'Testing admin force off duty functionality'
        };

        const forceOffResponse = await axios.post(`${BASE_URL}/fivem/admin/force-off-duty`, adminAction);
        
        if (forceOffResponse.data.success) {
            console.log('✅ Admin force off duty successful');
            console.log(`   Player: ${forceOffResponse.data.data.playerName}`);
            console.log(`   Job: ${forceOffResponse.data.data.job}`);
            console.log(`   Sessions ended: ${forceOffResponse.data.data.sessionsEnded}`);
            console.log(`   Admin: ${forceOffResponse.data.data.adminName}`);
            console.log(`   Reason: ${forceOffResponse.data.data.reason}`);
            console.log(`   Time: ${new Date(forceOffResponse.data.data.timestamp).toLocaleString()}`);
        } else {
            console.log('❌ Admin force off duty failed:', forceOffResponse.data.message);
            return;
        }

        await new Promise(resolve => setTimeout(resolve, 1000));

        // Verify player is no longer in active sessions
        console.log('\n4. Verifying player removed from active sessions...');
        const activeResponse2 = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        const stillFoundPlayer = activeResponse2.data.data.find(session => 
            session.identifier === testPlayer.identifier
        );
        
        if (!stillFoundPlayer) {
            console.log('✅ Test player successfully removed from active sessions');
        } else {
            console.log('❌ Test player still in active sessions');
        }

        // Check duty logs for admin action
        console.log('\n5. Checking duty logs for admin action...');
        const logsResponse = await axios.get(`${BASE_URL}/fivem/duty-logs?limit=10`);
        const adminLog = logsResponse.data.data.find(log => 
            log.identifier === testPlayer.identifier && 
            log.status === 'off_duty' &&
            log.notes && log.notes.includes('Test Admin')
        );
        
        if (adminLog) {
            console.log('✅ Admin action logged in duty logs');
            console.log(`   Log: ${adminLog.name} - ${adminLog.status} - ${new Date(adminLog.timestamp).toLocaleString()}`);
            console.log(`   Notes: ${adminLog.notes}`);
        } else {
            console.log('❌ Admin action not found in duty logs');
        }

        console.log('\n🎉 Admin force off duty test completed!\n');

    } catch (error) {
        console.error('❌ Admin test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Run test
testAdminForceOffDuty();
