Config = {}

-- Localization
Config.Locale = 'vi'

-- Node.js Integration
Config.NodeServer = {
    enabled = true,
    url = 'http://localhost:3000/api/fivem',
    timeout = 10000,
    retries = 3
}

-- Duty System Settings
Config.DutyJobs = {
    'police',
    'police2', 
    'ambulance',
    'mechanic',
    'tiembanh',
    'army',
    'offpolice',
    'offpolice2',
    'offambulance', 
    'offmechanic',
    'offtiembanh',
    'offarmy'
}

-- Marker Settings
Config.DrawDistance = 100.0
Config.InteractionDistance = 1.5

-- Duty Zones/Markers
Config.Zones = {
    PoliceDuty = {
        Pos = { x = 441.7989, y = -982.0529, z = 30.6896 },
        Size = { x = 1.5, y = 1.5, z = 1.0 },
        Color = { r = 0, g = 0, b = 255 },
        Type = 27
    },
    
    Police2Duty = {
        Pos = { x = 1853.1, y = 3689.63, z = 34.27 },
        Size = { x = 1.5, y = 1.5, z = 1.0 },
        Color = { r = 0, g = 0, b = 255 },
        Type = 27
    },
    
    AmbulanceDuty = {
        Pos = { x = 307.6, y = -1433.4, z = 29.9 },
        Size = { x = 1.5, y = 1.5, z = 1.0 },
        Color = { r = 255, g = 0, b = 0 },
        Type = 27
    },
    
    MechanicDuty = {
        Pos = { x = -347.4, y = -133.3, z = 39.0 },
        Size = { x = 1.5, y = 1.5, z = 1.0 },
        Color = { r = 255, g = 165, b = 0 },
        Type = 27
    },
    
    FoodDuty = {
        Pos = { x = -1196.3, y = -901.3, z = 13.9 },
        Size = { x = 1.5, y = 1.5, z = 1.0 },
        Color = { r = 255, g = 192, b = 203 },
        Type = 27
    }
}

-- Job Configuration for Dashboard
Config.JobConfig = {
    ['police'] = {
        name = 'Cảnh Sát',
        icon = 'fas fa-shield-alt',
        color = '#1e40af',
        description = 'Lực lượng cảnh sát'
    },
    ['police2'] = {
        name = 'Cảnh Sát 2',
        icon = 'fas fa-user-shield', 
        color = '#7c3aed',
        description = 'Lực lượng cảnh sát đặc biệt'
    },
    ['ambulance'] = {
        name = 'Y Tế',
        icon = 'fas fa-ambulance',
        color = '#059669',
        description = 'Dịch vụ y tế khẩn cấp'
    },
    ['mechanic'] = {
        name = 'Thợ Máy',
        icon = 'fas fa-wrench',
        color = '#dc2626',
        description = 'Dịch vụ sửa chữa xe'
    },
    ['tiembanh'] = {
        name = 'Tiệm Bánh',
        icon = 'fas fa-birthday-cake',
        color = '#d97706',
        description = 'Cửa hàng bánh ngọt'
    },
    ['army'] = {
        name = 'Quân Đội',
        icon = 'fas fa-fighter-jet',
        color = '#374151',
        description = 'Lực lượng quân đội'
    }
}

-- Notification Settings
Config.Notifications = {
    enabled = true,
    type = 'dopeNotify', -- 'dopeNotify', 'mythic_notify', 'esx', 'native'
    duration = 4000
}

-- Debug Settings
Config.Debug = {
    enabled = true,
    logLevel = 'info' -- 'error', 'warn', 'info', 'debug'
}

-- Reconnect Menu Settings
Config.ReconnectMenu = {
    enabled = true,
    autoShow = true,
    timeout = 30000, -- 30 seconds to choose
    defaultAction = 'ignore'
}

-- Session Management
Config.SessionManagement = {
    autoCleanup = true,
    maxSessionHours = 24,
    cleanupInterval = 3600000 -- 1 hour in milliseconds
}

-- Web Dashboard Settings
Config.WebDashboard = {
    enabled = true,
    url = 'http://localhost:3000',
    autoRefresh = 30000, -- 30 seconds
    showInChat = true
}
