// Simple test để kiểm tra disconnect handling
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testSimpleDisconnect() {
    console.log('🧪 Testing Simple Disconnect Handling...\n');

    try {
        // 1. Tạo player on duty
        console.log('1. Creating player on duty...');
        const player = {
            identifier: 'steam:test_disconnect_123',
            name: 'Test Player Disconnect',
            job: 'police',
            grade: 1,
            status: 'on_duty'
        };

        const onDutyResponse = await axios.post(`${BASE_URL}/fivem/duty-log`, player);
        console.log('✅ Player on duty:', onDutyResponse.data.message);
        
        // Wait 2 seconds
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 2. Check active sessions
        console.log('\n2. Checking active sessions...');
        const activeResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse.data.total}`);
        
        if (activeResponse.data.total > 0) {
            console.log('   Sessions found:');
            activeResponse.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
            });
        }

        // 3. Simulate disconnect
        console.log('\n3. Simulating player disconnect...');
        const disconnectData = {
            identifier: player.identifier,
            name: player.name,
            reason: 'test_disconnect'
        };

        const disconnectResponse = await axios.post(`${BASE_URL}/fivem/player-disconnect`, disconnectData);
        console.log('✅ Disconnect response:', disconnectResponse.data.message);
        
        if (disconnectResponse.data.data) {
            console.log('📊 Sessions ended:', disconnectResponse.data.data.sessionsEnded);
            console.log('⏰ Disconnect time:', disconnectResponse.data.data.disconnectTime);
        }

        // 4. Check active sessions again
        console.log('\n4. Checking active sessions after disconnect...');
        const activeResponse2 = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse2.data.total}`);
        
        if (activeResponse2.data.total === 0) {
            console.log('🎉 Perfect! No active sessions - disconnect handled correctly!');
        } else {
            console.log('⚠️  Still have active sessions:');
            activeResponse2.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
            });
        }

        // 5. Check recent duty logs
        console.log('\n5. Checking recent duty logs...');
        const logsResponse = await axios.get(`${BASE_URL}/fivem/duty-logs?limit=5`);
        console.log('✅ Recent logs:');
        
        logsResponse.data.data.slice(0, 5).forEach((log, index) => {
            const time = new Date(log.timestamp).toLocaleTimeString();
            console.log(`   ${index + 1}. ${log.name} (${log.job}) - ${log.status} - ${time}`);
        });

        console.log('\n🎉 Simple disconnect test completed!');
        console.log('\n📋 Summary:');
        console.log('✅ Player can go on duty');
        console.log('✅ Active sessions are tracked');
        console.log('✅ Disconnect is processed');
        console.log('✅ Sessions are automatically ended');
        console.log('✅ Logs are created');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy test
testSimpleDisconnect();
