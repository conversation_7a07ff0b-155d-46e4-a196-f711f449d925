// Test tính năng auto off-duty khi player mất job
const axios = require('axios');

const BASE_URL = 'https://med.kaitomc.site/api';

async function testJobLost() {
    console.log('💼 Testing Auto Off-Duty When Player Loses Job...\n');

    try {
        // 1. Tạo test player on duty
        console.log('1. Creating test player on duty...');
        const testPlayer = {
            identifier: 'steam:job_lost_test_999',
            name: 'Job Lost Test Player',
            job: 'police',
            grade: 2,
            status: 'on_duty'
        };

        const onDutyResponse = await axios.post(`${BASE_URL}/fivem/duty-log`, testPlayer);
        console.log('✅ Test player on duty:', onDutyResponse.data.message);
        
        // Wait để session được tạo
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 2. Kiểm tra active sessions
        console.log('\n2. Checking active sessions before job loss...');
        const activeResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse.data.total}`);
        
        let foundTestPlayer = false;
        if (activeResponse.data.data && activeResponse.data.data.length > 0) {
            console.log('   Current active sessions:');
            activeResponse.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
                if (session.identifier === testPlayer.identifier) {
                    foundTestPlayer = true;
                }
            });
        }

        console.log(`   Test player found in active sessions: ${foundTestPlayer ? '✅' : '❌'}`);

        // 3. Simulate job loss (player bị fire hoặc chuyển job)
        console.log('\n3. Simulating job loss (player gets fired/job changed)...');
        const jobLostData = {
            identifier: testPlayer.identifier,
            name: testPlayer.name,
            reason: 'job_lost - fired by admin'
        };

        const jobLostResponse = await axios.post(`${BASE_URL}/fivem/player-disconnect`, jobLostData);
        console.log('✅ Job loss processed:', jobLostResponse.data.message);
        
        if (jobLostResponse.data.data) {
            console.log('📊 Sessions ended:', jobLostResponse.data.data.sessionsEnded);
            console.log('⏰ Job lost time:', new Date(jobLostResponse.data.data.disconnectTime).toLocaleString());
        }

        // 4. Kiểm tra active sessions sau job loss
        console.log('\n4. Checking active sessions after job loss...');
        const activeResponse2 = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse2.data.total}`);
        
        let stillFoundTestPlayer = false;
        if (activeResponse2.data.data && activeResponse2.data.data.length > 0) {
            console.log('   Remaining active sessions:');
            activeResponse2.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
                if (session.identifier === testPlayer.identifier) {
                    stillFoundTestPlayer = true;
                }
            });
        } else {
            console.log('   No active sessions remaining');
        }

        console.log(`   Test player still in active sessions: ${stillFoundTestPlayer ? '❌' : '✅'}`);

        // 5. Kiểm tra duty logs để xem job lost entry
        console.log('\n5. Checking duty logs for job lost entries...');
        const logsResponse = await axios.get(`${BASE_URL}/fivem/duty-logs?limit=15`);
        console.log('✅ Recent duty logs:');
        
        let foundJobLostOffDuty = false;
        let foundManualOnDuty = false;
        
        logsResponse.data.data.slice(0, 15).forEach((log, index) => {
            const time = new Date(log.timestamp).toLocaleString();
            const isTestPlayer = log.identifier === testPlayer.identifier;
            
            if (isTestPlayer) {
                if (log.status === 'off_duty') {
                    foundJobLostOffDuty = true;
                }
                if (log.status === 'on_duty') {
                    foundManualOnDuty = true;
                }
            }
            
            const marker = isTestPlayer ? 
                (log.status === 'off_duty' ? '💼 JOB LOST' : '👤 MANUAL ON') : 
                '   ';
            const statusEmoji = log.status === 'on_duty' ? '🟢' : '🔴';
            
            console.log(`   ${index + 1}. ${marker} ${statusEmoji} ${log.name} (${log.job}) - ${log.status} - ${time}`);
        });

        console.log(`   Found manual on-duty log: ${foundManualOnDuty ? '✅' : '❌'}`);
        console.log(`   Found job lost off-duty log: ${foundJobLostOffDuty ? '✅' : '❌'}`);

        // 6. Kiểm tra completed sessions
        console.log('\n6. Checking completed duty sessions...');
        const sessionsResponse = await axios.get(`${BASE_URL}/fivem/duty-sessions?limit=5`);
        console.log('✅ Recent completed sessions:');
        
        let foundCompletedSession = false;
        sessionsResponse.data.data.slice(0, 5).forEach((session, index) => {
            const isTestPlayer = session.identifier === testPlayer.identifier;
            const startTime = new Date(session.start_time).toLocaleString();
            const endTime = session.end_time ? new Date(session.end_time).toLocaleString() : 'Still active';
            const duration = session.duration_minutes ? `${Math.floor(session.duration_minutes / 60)}h ${session.duration_minutes % 60}m` : 'N/A';
            
            if (isTestPlayer && session.end_time) {
                foundCompletedSession = true;
            }
            
            const marker = isTestPlayer ? '💼 JOB LOST' : '   ';
            console.log(`   ${index + 1}. ${marker} ${session.name} (${session.job})`);
            console.log(`      Start: ${startTime}`);
            console.log(`      End: ${endTime}`);
            console.log(`      Duration: ${duration}`);
            console.log(`      Status: ${session.status}`);
        });

        console.log(`   Found completed session: ${foundCompletedSession ? '✅' : '❌'}`);

        console.log('\n🎉 Job Lost Auto Off-Duty Test Completed!');
        console.log('\n📋 Test Results Summary:');
        console.log(`✅ Player on duty created: ${onDutyResponse.status === 200 ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Found in active sessions: ${foundTestPlayer ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Job loss processed: ${jobLostResponse.status === 200 ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Removed from active: ${!stillFoundTestPlayer ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Job lost off-duty logged: ${foundJobLostOffDuty ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Session completed: ${foundCompletedSession ? 'PASS' : 'FAIL'}`);
        
        const allTestsPassed = onDutyResponse.status === 200 && 
                              foundTestPlayer && 
                              jobLostResponse.status === 200 && 
                              !stillFoundTestPlayer && 
                              foundJobLostOffDuty && 
                              foundCompletedSession;
        
        console.log(`\n🎯 Overall Test Result: ${allTestsPassed ? '🎉 ALL TESTS PASSED!' : '⚠️  SOME TESTS FAILED'}`);
        
        console.log('\n💼 What happens when player loses job in FiveM:');
        console.log('📱 Scenarios that trigger auto off-duty:');
        console.log('   1. 🔥 Admin fires player (police → unemployed)');
        console.log('   2. 🔄 Admin changes job (police → civilian)');
        console.log('   3. 📝 Player quits job voluntarily');
        console.log('   4. ⚖️ Player gets demoted to non-duty job');
        console.log('');
        console.log('🔧 Technical Process:');
        console.log('   1. 🎮 ESX triggers esx:setJob event');
        console.log('   2. 📊 Client detects job change (duty → non-duty)');
        console.log('   3. 🌐 Client sends duty:jobLost to server');
        console.log('   4. 💾 Server logs off_duty to all systems');
        console.log('   5. 📈 Web dashboard updates statistics');
        console.log('');
        console.log('✅ Benefits:');
        console.log('   - Accurate duty statistics');
        console.log('   - No ghost active sessions');
        console.log('   - Proper session completion');
        console.log('   - Clean data integrity');
        
        console.log('\n🚀 To apply in FiveM:');
        console.log('1. Copy updated client.lua and server.lua');
        console.log('2. Restart resource: restart esx_duty_advanced');
        console.log('3. Test: Player on duty → admin changes job → auto off duty');
        console.log('4. Verify: Check web dashboard for completed session');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy test
testJobLost();
