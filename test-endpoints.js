// File test để kiểm tra các endpoint
// Chạy: node test-endpoints.js (sau khi server đã chạy)

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testEndpoints() {
    console.log('🚀 Bắt đầu test các endpoints...\n');

    try {
        // Test health check
        console.log('1. Testing Health Check...');
        const healthResponse = await axios.get(`${BASE_URL}/health`);
        console.log('✅ Health check:', healthResponse.data.message);
        console.log('');

        // Test onduty endpoint
        console.log('2. Testing Onduty Endpoint...');
        const ondutyData = {
            employeeId: 'EMP001',
            employeeName: 'Nguyễn Văn A',
            location: 'Văn phòng Hà Nội',
            note: 'Bắt đầu ca làm việc sáng'
        };
        
        const ondutyResponse = await axios.post(`${BASE_URL}/api/onduty`, ondutyData);
        console.log('✅ Onduty response:', ondutyResponse.data.message);
        console.log('📝 Data:', JSON.stringify(ondutyResponse.data.data, null, 2));
        console.log('');

        // Test offduty endpoint
        console.log('3. Testing Offduty Endpoint...');
        const offdutyData = {
            employeeId: 'EMP001',
            employeeName: 'Nguyễn Văn A',
            location: 'Văn phòng Hà Nội',
            note: 'Kết thúc ca làm việc'
        };
        
        const offdutyResponse = await axios.post(`${BASE_URL}/api/offduty`, offdutyData);
        console.log('✅ Offduty response:', offdutyResponse.data.message);
        console.log('📝 Data:', JSON.stringify(offdutyResponse.data.data, null, 2));
        console.log('');

        // Test thêm một nhân viên khác
        console.log('4. Testing với nhân viên khác...');
        const employee2OndutyData = {
            employeeId: 'EMP002',
            employeeName: 'Trần Thị B',
            location: 'Văn phòng HCM',
            note: 'Check-in ca chiều'
        };
        
        await axios.post(`${BASE_URL}/api/onduty`, employee2OndutyData);
        console.log('✅ Đã thêm onduty cho nhân viên EMP002');
        console.log('');

        // Test get all attendance
        console.log('5. Testing Get All Attendance...');
        const allAttendanceResponse = await axios.get(`${BASE_URL}/api/attendance`);
        console.log('✅ Get all attendance:', allAttendanceResponse.data.message);
        console.log('📊 Total records:', allAttendanceResponse.data.total);
        console.log('📝 Records:');
        allAttendanceResponse.data.data.forEach((record, index) => {
            console.log(`   ${index + 1}. ${record.employeeName} - ${record.type} - ${record.timestamp}`);
        });
        console.log('');

        // Test filter by employeeId
        console.log('6. Testing Filter by Employee ID...');
        const filteredResponse = await axios.get(`${BASE_URL}/api/attendance?employeeId=EMP001`);
        console.log('✅ Filter by EMP001:', filteredResponse.data.message);
        console.log('📊 Filtered records:', filteredResponse.data.total);
        console.log('');

        // Test filter by type
        console.log('7. Testing Filter by Type...');
        const typeFilterResponse = await axios.get(`${BASE_URL}/api/attendance?type=onduty`);
        console.log('✅ Filter by onduty:', typeFilterResponse.data.message);
        console.log('📊 Onduty records:', typeFilterResponse.data.total);
        console.log('');

        // Test get single record
        if (allAttendanceResponse.data.data.length > 0) {
            console.log('8. Testing Get Single Record...');
            const firstRecordId = allAttendanceResponse.data.data[0].id;
            const singleRecordResponse = await axios.get(`${BASE_URL}/api/attendance/${firstRecordId}`);
            console.log('✅ Get single record:', singleRecordResponse.data.message);
            console.log('📝 Record:', singleRecordResponse.data.data.employeeName, '-', singleRecordResponse.data.data.type);
            console.log('');
        }

        // Test error case - missing required fields
        console.log('9. Testing Error Case - Missing Required Fields...');
        try {
            await axios.post(`${BASE_URL}/api/onduty`, {
                // Thiếu employeeId và employeeName
                location: 'Test location'
            });
        } catch (error) {
            console.log('✅ Error handling works:', error.response.data.message);
        }
        console.log('');

        console.log('🎉 Tất cả tests đã hoàn thành thành công!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy tests
testEndpoints();
