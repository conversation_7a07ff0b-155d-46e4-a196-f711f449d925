// Test từng endpoint để tìm lỗi
const axios = require('axios');

async function testEndpoints() {
    const BASE_URL = 'http://localhost:3000';
    
    console.log('🧪 Testing individual endpoints...\n');

    // Test 1: Health check
    try {
        console.log('1. Testing health endpoint...');
        const health = await axios.get(`${BASE_URL}/health`);
        console.log('✅ Health:', health.data.message);
    } catch (error) {
        console.log('❌ Health failed:', error.message);
    }

    // Test 2: Root endpoint
    try {
        console.log('\n2. Testing root endpoint...');
        const root = await axios.get(`${BASE_URL}/`);
        console.log('✅ Root endpoint works');
    } catch (error) {
        console.log('❌ Root failed:', error.message);
    }

    // Test 3: Active sessions endpoint
    try {
        console.log('\n3. Testing active sessions endpoint...');
        const active = await axios.get(`${BASE_URL}/api/fivem/active-sessions`);
        console.log('✅ Active sessions:', active.data.total);
        
        if (active.data.data && active.data.data.length > 0) {
            console.log('   Sessions found:');
            active.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job})`);
            });
        }
    } catch (error) {
        console.log('❌ Active sessions failed:', error.message);
        if (error.response) {
            console.log('   Status:', error.response.status);
            console.log('   Data:', error.response.data);
        }
    }

    // Test 4: Duty logs endpoint
    try {
        console.log('\n4. Testing duty logs endpoint...');
        const logs = await axios.get(`${BASE_URL}/api/fivem/duty-logs?limit=3`);
        console.log('✅ Duty logs:', logs.data.data.length, 'records');
        
        if (logs.data.data && logs.data.data.length > 0) {
            console.log('   Recent logs:');
            logs.data.data.slice(0, 3).forEach((log, index) => {
                console.log(`   ${index + 1}. ${log.name} (${log.job}) - ${log.status}`);
            });
        }
    } catch (error) {
        console.log('❌ Duty logs failed:', error.message);
    }

    // Test 5: Player disconnect endpoint
    try {
        console.log('\n5. Testing player disconnect endpoint...');
        const disconnectData = {
            identifier: 'steam:test123',
            name: 'Test Player',
            reason: 'test'
        };
        
        const disconnect = await axios.post(`${BASE_URL}/api/fivem/player-disconnect`, disconnectData);
        console.log('✅ Player disconnect:', disconnect.data.message);
    } catch (error) {
        console.log('❌ Player disconnect failed:', error.message);
        if (error.response) {
            console.log('   Status:', error.response.status);
            console.log('   Data:', error.response.data);
        }
    }

    console.log('\n🎯 Endpoint testing completed!');
}

testEndpoints();
