// Test tính năng auto off-duty hoàn chỉnh (cả web và game)
const axios = require('axios');

const BASE_URL = 'https://med.kaitomc.site/api';

async function testFullAutoOffDuty() {
    console.log('🔄 Testing Full Auto Off-Duty System (Web + Game)...\n');

    try {
        // 1. Tạo test player on duty
        console.log('1. Creating test player on duty...');
        const testPlayer = {
            identifier: 'steam:full_auto_test_456',
            name: 'Full Auto Test Player',
            job: 'police',
            grade: 3,
            status: 'on_duty'
        };

        const onDutyResponse = await axios.post(`${BASE_URL}/fivem/duty-log`, testPlayer);
        console.log('✅ Test player on duty:', onDutyResponse.data.message);
        
        // Wait để session được tạo
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 2. <PERSON><PERSON><PERSON> tra active sessions
        console.log('\n2. Checking active sessions before disconnect...');
        const activeResponse = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse.data.total}`);
        
        let foundTestPlayer = false;
        if (activeResponse.data.data && activeResponse.data.data.length > 0) {
            console.log('   Current active sessions:');
            activeResponse.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
                if (session.identifier === testPlayer.identifier) {
                    foundTestPlayer = true;
                }
            });
        }

        console.log(`   Test player found in active sessions: ${foundTestPlayer ? '✅' : '❌'}`);

        // 3. Simulate player disconnect (auto off-duty)
        console.log('\n3. Simulating player disconnect (full auto off-duty)...');
        const disconnectData = {
            identifier: testPlayer.identifier,
            name: testPlayer.name,
            reason: 'Lost connection - full auto test'
        };

        const disconnectResponse = await axios.post(`${BASE_URL}/fivem/player-disconnect`, disconnectData);
        console.log('✅ Disconnect processed:', disconnectResponse.data.message);
        
        if (disconnectResponse.data.data) {
            console.log('📊 Sessions ended:', disconnectResponse.data.data.sessionsEnded);
            console.log('⏰ Disconnect time:', new Date(disconnectResponse.data.data.disconnectTime).toLocaleString());
        }

        // 4. Kiểm tra active sessions sau disconnect
        console.log('\n4. Checking active sessions after disconnect...');
        const activeResponse2 = await axios.get(`${BASE_URL}/fivem/active-sessions`);
        console.log(`✅ Active sessions: ${activeResponse2.data.total}`);
        
        let stillFoundTestPlayer = false;
        if (activeResponse2.data.data && activeResponse2.data.data.length > 0) {
            console.log('   Remaining active sessions:');
            activeResponse2.data.data.forEach((session, index) => {
                console.log(`   ${index + 1}. ${session.name} (${session.job}) - ${session.time_active}`);
                if (session.identifier === testPlayer.identifier) {
                    stillFoundTestPlayer = true;
                }
            });
        } else {
            console.log('   No active sessions remaining');
        }

        console.log(`   Test player still in active sessions: ${stillFoundTestPlayer ? '❌' : '✅'}`);

        // 5. Kiểm tra duty logs để xem auto off-duty entries
        console.log('\n5. Checking duty logs for auto off-duty entries...');
        const logsResponse = await axios.get(`${BASE_URL}/fivem/duty-logs?limit=15`);
        console.log('✅ Recent duty logs:');
        
        let foundAutoOffDuty = false;
        let foundManualOnDuty = false;
        
        logsResponse.data.data.slice(0, 15).forEach((log, index) => {
            const time = new Date(log.timestamp).toLocaleString();
            const isTestPlayer = log.identifier === testPlayer.identifier;
            
            if (isTestPlayer) {
                if (log.status === 'off_duty') {
                    foundAutoOffDuty = true;
                }
                if (log.status === 'on_duty') {
                    foundManualOnDuty = true;
                }
            }
            
            const marker = isTestPlayer ? 
                (log.status === 'off_duty' ? '🤖 AUTO OFF' : '👤 MANUAL ON') : 
                '   ';
            const statusEmoji = log.status === 'on_duty' ? '🟢' : '🔴';
            
            console.log(`   ${index + 1}. ${marker} ${statusEmoji} ${log.name} (${log.job}) - ${log.status} - ${time}`);
        });

        console.log(`   Found manual on-duty log: ${foundManualOnDuty ? '✅' : '❌'}`);
        console.log(`   Found auto off-duty log: ${foundAutoOffDuty ? '✅' : '❌'}`);

        // 6. Kiểm tra completed sessions
        console.log('\n6. Checking completed duty sessions...');
        const sessionsResponse = await axios.get(`${BASE_URL}/fivem/duty-sessions?limit=5`);
        console.log('✅ Recent completed sessions:');
        
        let foundCompletedSession = false;
        sessionsResponse.data.data.slice(0, 5).forEach((session, index) => {
            const isTestPlayer = session.identifier === testPlayer.identifier;
            const startTime = new Date(session.start_time).toLocaleString();
            const endTime = session.end_time ? new Date(session.end_time).toLocaleString() : 'Still active';
            const duration = session.duration_minutes ? `${Math.floor(session.duration_minutes / 60)}h ${session.duration_minutes % 60}m` : 'N/A';
            
            if (isTestPlayer && session.end_time) {
                foundCompletedSession = true;
            }
            
            const marker = isTestPlayer ? '🎯 TEST' : '   ';
            console.log(`   ${index + 1}. ${marker} ${session.name} (${session.job})`);
            console.log(`      Start: ${startTime}`);
            console.log(`      End: ${endTime}`);
            console.log(`      Duration: ${duration}`);
            console.log(`      Status: ${session.status}`);
        });

        console.log(`   Found completed session: ${foundCompletedSession ? '✅' : '❌'}`);

        console.log('\n🎉 Full Auto Off-Duty Test Completed!');
        console.log('\n📋 Test Results Summary:');
        console.log(`✅ Player on duty created: ${onDutyResponse.status === 200 ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Found in active sessions: ${foundTestPlayer ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Disconnect processed: ${disconnectResponse.status === 200 ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Removed from active: ${!stillFoundTestPlayer ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Auto off-duty logged: ${foundAutoOffDuty ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Session completed: ${foundCompletedSession ? 'PASS' : 'FAIL'}`);
        
        const allTestsPassed = onDutyResponse.status === 200 && 
                              foundTestPlayer && 
                              disconnectResponse.status === 200 && 
                              !stillFoundTestPlayer && 
                              foundAutoOffDuty && 
                              foundCompletedSession;
        
        console.log(`\n🎯 Overall Test Result: ${allTestsPassed ? '🎉 ALL TESTS PASSED!' : '⚠️  SOME TESTS FAILED'}`);
        
        console.log('\n🔧 What happens now in FiveM:');
        console.log('📱 When player disconnects while on duty:');
        console.log('   1. 🎮 Game: Job automatically switches (police → offpolice)');
        console.log('   2. 🌐 Web: Session ends and statistics update');
        console.log('   3. 📝 Logs: Auto off-duty entry created');
        console.log('   4. 💾 Database: Player job saved as off-duty');
        console.log('');
        console.log('🔄 When player reconnects:');
        console.log('   1. 🎮 Game: Player spawns with off-duty job (offpolice)');
        console.log('   2. 🌐 Web: No active session (clean state)');
        console.log('   3. ✅ Player must manually go on duty again');
        
        console.log('\n🚀 To apply in FiveM:');
        console.log('1. Copy updated server.lua to your resource');
        console.log('2. Restart resource: restart esx_duty_advanced');
        console.log('3. Test: go on duty → disconnect → reconnect');
        console.log('4. Verify: job should be off-duty when reconnecting');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy test
testFullAutoOffDuty();
