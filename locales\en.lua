Locales['en'] = {
    -- Duty System
    ['duty'] = 'Duty',
    ['duty_on'] = 'Go On Duty',
    ['duty_off'] = 'Go Off Duty',
    ['duty_enabled'] = 'You are now ON DUTY!',
    ['duty_disabled'] = 'You are now OFF DUTY!',
    ['duty_not_authorized'] = 'You are not authorized to use duty system!',
    ['duty_already_on'] = 'You are already on duty!',
    ['duty_already_off'] = 'You are already off duty!',
    
    -- Markers
    ['press_e_duty'] = 'Press [E] to toggle Duty status',
    ['press_e_duty_on'] = 'Press [E] to go ON DUTY',
    ['press_e_duty_off'] = 'Press [E] to go OFF DUTY',
    
    -- Reconnect System
    ['reconnect_title'] = 'DUTY RECONNECT SYSTEM',
    ['reconnect_message'] = 'Welcome back!\n\nYou have an active %s session from %s ago.\n\nWhat would you like to do:',
    ['reconnect_continue'] = 'Continue old session',
    ['reconnect_continue_desc'] = 'Continue working from previous session',
    ['reconnect_end'] = 'End old session',
    ['reconnect_end_desc'] = 'End session and go off duty',
    ['reconnect_end_new'] = 'End and start new',
    ['reconnect_end_new_desc'] = 'End old session and start a new one',
    ['reconnect_ignore'] = 'Ignore',
    ['reconnect_ignore_desc'] = 'Ignore old session (not recommended)',
    ['reconnect_instructions'] = '⬆️⬇️ Navigate | ENTER Select | BACKSPACE Cancel',
    
    -- Notifications
    ['notification_duty_on'] = 'You are now ON DUTY!',
    ['notification_duty_off'] = 'You are now OFF DUTY!',
    ['notification_reconnect_continue'] = 'Continued old duty session',
    ['notification_reconnect_end'] = 'Ended old session',
    ['notification_reconnect_end_new'] = 'Ended old session and started new',
    ['notification_reconnect_ignore'] = 'Ignored old session',
    ['notification_reconnect_cancelled'] = 'Cancelled - ignored old session',
    
    -- Errors
    ['error_job_not_supported'] = 'Your job does not support duty system!',
    ['error_no_permission'] = 'You do not have permission to use this command!',
    ['error_server_connection'] = 'Server connection error!',
    ['error_invalid_action'] = 'Invalid action!',
    
    -- Commands
    ['command_dutystats'] = 'View duty statistics on web dashboard',
    ['command_dutycleanup'] = 'Cleanup old duty sessions',
    ['command_testdutymenu'] = 'Test reconnect menu (admin only)',
    
    -- Web Dashboard
    ['dashboard_url'] = 'Web Dashboard: %s',
    ['dashboard_check'] = 'Check web dashboard at: %s',
    ['cleanup_completed'] = 'Successfully cleaned up %d old sessions!',
    ['cleanup_failed'] = 'Cleanup failed: %s',
    
    -- Time formats
    ['time_hours'] = '%d hours',
    ['time_minutes'] = '%d minutes',
    ['time_hours_minutes'] = '%d hours %d minutes',
    ['time_just_now'] = 'just now',
    ['time_ago'] = '%s ago',
    
    -- Jobs
    ['job_police'] = 'Police',
    ['job_police2'] = 'Special Police',
    ['job_ambulance'] = 'EMS',
    ['job_mechanic'] = 'Mechanic',
    ['job_tiembanh'] = 'Bakery',
    ['job_army'] = 'Army',
    
    -- Status
    ['status_on_duty'] = 'On Duty',
    ['status_off_duty'] = 'Off Duty',
    ['status_active'] = 'Active',
    ['status_inactive'] = 'Inactive',
    
    -- Session info
    ['session_started'] = 'Session started: %s',
    ['session_ended'] = 'Session ended: %s',
    ['session_duration'] = 'Duration: %s',
    ['session_active_for'] = 'Active for: %s',
    
    -- Warnings
    ['warning_long_session'] = 'This session has been active for too long (%s), did you forget to go off duty?',
    ['warning_disconnect'] = 'You disconnected while on duty. Session has been automatically ended.',
    ['warning_cleanup'] = 'Sessions older than %d hours will be automatically cleaned up.',
}
