# Web Điểm Danh API Server

Server API để xử lý thông tin onduty và offduty của nhân viên.

## Cài đặt

1. Cài đặt dependencies:
```bash
npm install
```

2. Chạy server:
```bash
npm start
```

Hoặc chạy ở chế độ development:
```bash
npm run dev
```

Server sẽ chạy tại `http://localhost:3000`

## API Endpoints

### 1. POST /api/onduty
Ghi nhận thông tin nhân viên onduty (bắt đầu ca làm việc)

**Request Body:**
```json
{
  "employeeId": "EMP001",
  "employeeName": "Nguyễn Văn A",
  "timestamp": "2024-01-15T08:00:00.000Z",
  "location": "Văn phòng HN",
  "note": "Bắt đầu ca sáng"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Đ<PERSON> ghi nhận thông tin onduty thành công",
  "data": {
    "id": "1705305600000",
    "employeeId": "EMP001",
    "employeeName": "Nguyễn Văn A",
    "type": "onduty",
    "timestamp": "2024-01-15T08:00:00.000Z",
    "location": "Văn phòng HN",
    "note": "Bắt đầu ca sáng",
    "createdAt": "2024-01-15T08:00:00.000Z"
  }
}
```

### 2. POST /api/offduty
Ghi nhận thông tin nhân viên offduty (kết thúc ca làm việc)

**Request Body:**
```json
{
  "employeeId": "EMP001",
  "employeeName": "Nguyễn Văn A",
  "timestamp": "2024-01-15T17:00:00.000Z",
  "location": "Văn phòng HN",
  "note": "Kết thúc ca làm việc"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Đã ghi nhận thông tin offduty thành công",
  "data": {
    "id": "1705338000000",
    "employeeId": "EMP001",
    "employeeName": "Nguyễn Văn A",
    "type": "offduty",
    "timestamp": "2024-01-15T17:00:00.000Z",
    "location": "Văn phòng HN",
    "note": "Kết thúc ca làm việc",
    "createdAt": "2024-01-15T17:00:00.000Z"
  }
}
```

### 3. GET /api/attendance
Lấy danh sách tất cả records điểm danh

**Query Parameters (optional):**
- `employeeId`: Lọc theo ID nhân viên
- `type`: Lọc theo loại (onduty/offduty)
- `date`: Lọc theo ngày (format: YYYY-MM-DD)

**Examples:**
- `GET /api/attendance` - Lấy tất cả records
- `GET /api/attendance?employeeId=EMP001` - Lấy records của nhân viên EMP001
- `GET /api/attendance?type=onduty` - Lấy tất cả records onduty
- `GET /api/attendance?date=2024-01-15` - Lấy records ngày 15/01/2024

**Response:**
```json
{
  "success": true,
  "message": "Lấy danh sách attendance thành công",
  "data": [
    {
      "id": "1705305600000",
      "employeeId": "EMP001",
      "employeeName": "Nguyễn Văn A",
      "type": "onduty",
      "timestamp": "2024-01-15T08:00:00.000Z",
      "location": "Văn phòng HN",
      "note": "Bắt đầu ca sáng",
      "createdAt": "2024-01-15T08:00:00.000Z"
    }
  ],
  "total": 1
}
```

### 4. GET /api/attendance/:id
Lấy thông tin một record cụ thể

**Response:**
```json
{
  "success": true,
  "message": "Lấy thông tin record thành công",
  "data": {
    "id": "1705305600000",
    "employeeId": "EMP001",
    "employeeName": "Nguyễn Văn A",
    "type": "onduty",
    "timestamp": "2024-01-15T08:00:00.000Z",
    "location": "Văn phòng HN",
    "note": "Bắt đầu ca sáng",
    "createdAt": "2024-01-15T08:00:00.000Z"
  }
}
```

### 5. GET /health
Kiểm tra trạng thái server

**Response:**
```json
{
  "success": true,
  "message": "Server đang hoạt động bình thường",
  "timestamp": "2024-01-15T08:00:00.000Z"
}
```

## Cấu trúc dữ liệu

### Attendance Record
```json
{
  "id": "string",           // ID duy nhất của record
  "employeeId": "string",   // ID nhân viên (bắt buộc)
  "employeeName": "string", // Tên nhân viên (bắt buộc)
  "type": "string",         // "onduty" hoặc "offduty"
  "timestamp": "string",    // Thời gian điểm danh (ISO string)
  "location": "string",     // Vị trí điểm danh (tùy chọn)
  "note": "string",         // Ghi chú (tùy chọn)
  "createdAt": "string"     // Thời gian tạo record
}
```

## Lưu ý

- Hiện tại dữ liệu được lưu trong memory (mảng), sẽ mất khi restart server
- Trong production nên sử dụng database như MongoDB, PostgreSQL, etc.
- Cần thêm authentication và authorization cho các endpoint
- Có thể thêm validation phức tạp hơn cho dữ liệu đầu vào
